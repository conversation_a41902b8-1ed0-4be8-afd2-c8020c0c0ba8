"""
Menus supplémentaires pour COMTABELITE
Gestion des produits, employés, transactions, déclarations et rapports
"""

from datetime import datetime
from models.produit import Produit
from models.employe import Employe
from models.transaction import Transaction
from models.declaration import DeclarationFiscale, DeclarationSociale
from models.client import Client
from models.fournisseur import Fournis<PERSON>ur
from utils.calculs import calculer_salaire, declarer_impot, generer_declarations_sociales_mois
from utils.rapports import afficher_rapport_mensuel, afficher_bilan_annuel
from utils.helpers import (
    saisir_choix, confirmer_action, valider_montant, valider_quantite,
    valider_cin, afficher_tableau, formater_montant, sauvegarder_backup
)

def menu_produits(self):
    """Menu de gestion des produits"""
    options = [
        "Ajouter un produit",
        "Lister tous les produits",
        "Modifier un produit",
        "Supprimer un produit",
        "Voir l'état des stocks",
        "Produits en stock faible",
        "Retour au menu principal"
    ]
    
    choix = saisir_choix(options, "=== GESTION DES PRODUITS ===")
    
    if choix == 0:
        self.ajouter_produit()
    elif choix == 1:
        self.lister_produits()
    elif choix == 2:
        self.modifier_produit()
    elif choix == 3:
        self.supprimer_produit()
    elif choix == 4:
        self.voir_etat_stocks()
    elif choix == 5:
        self.voir_stocks_faibles()

def ajouter_produit(self):
    """Ajoute un nouveau produit"""
    print("\n=== AJOUTER UN PRODUIT ===")
    
    nom = input("Nom du produit (ex: Gasoil, Essence Super): ").strip()
    if not nom:
        print("❌ Le nom est obligatoire")
        return
    
    prix_achat_str = input("Prix d'achat unitaire: ").strip()
    prix_achat = valider_montant(prix_achat_str)
    if prix_achat is None:
        return
    
    prix_vente_str = input("Prix de vente unitaire: ").strip()
    prix_vente = valider_montant(prix_vente_str)
    if prix_vente is None:
        return
    
    if prix_vente <= prix_achat:
        if not confirmer_action("⚠️ Prix de vente ≤ prix d'achat. Continuer ?"):
            return
    
    quantite_str = input("Quantité initiale (0 par défaut): ").strip()
    quantite = valider_quantite(quantite_str) if quantite_str else 0.0
    if quantite is None:
        quantite = 0.0
    
    unite = input("Unité de mesure (L par défaut): ").strip() or "L"
    
    produit = Produit(
        nom=nom,
        prix_achat=prix_achat,
        prix_vente=prix_vente,
        quantite=quantite,
        unite=unite
    )
    produit.save()
    
    marge = produit.calculer_marge()
    print(f"✅ Marge calculée: {marge:.1f}%")

def lister_produits(self):
    """Liste tous les produits"""
    produits = Produit.get_all()
    
    if not produits:
        print("\n📋 Aucun produit enregistré")
        return
    
    donnees = []
    for produit in produits:
        donnees.append({
            'ID': produit.id,
            'Nom': produit.nom,
            'Prix Achat': formater_montant(produit.prix_achat),
            'Prix Vente': formater_montant(produit.prix_vente),
            'Stock': f"{produit.quantite} {produit.unite}",
            'Marge': f"{produit.calculer_marge():.1f}%"
        })
    
    afficher_tableau(donnees, ['ID', 'Nom', 'Prix Achat', 'Prix Vente', 'Stock', 'Marge'], 
                    "LISTE DES PRODUITS")

def voir_etat_stocks(self):
    """Affiche l'état détaillé des stocks"""
    produits = Produit.get_all()
    
    if not produits:
        print("\n📋 Aucun produit en stock")
        return
    
    donnees = []
    valeur_totale = 0
    
    for produit in produits:
        valeur_stock = produit.calculer_valeur_stock()
        valeur_totale += valeur_stock
        
        donnees.append({
            'Produit': produit.nom,
            'Quantité': f"{produit.quantite} {produit.unite}",
            'Prix Achat': formater_montant(produit.prix_achat),
            'Valeur Stock': formater_montant(valeur_stock)
        })
    
    afficher_tableau(donnees, ['Produit', 'Quantité', 'Prix Achat', 'Valeur Stock'], 
                    "ÉTAT DES STOCKS")
    print(f"\n💰 Valeur totale du stock: {formater_montant(valeur_totale)}")

def voir_stocks_faibles(self):
    """Affiche les produits en stock faible"""
    seuil_str = input("Seuil d'alerte (100 par défaut): ").strip()
    seuil = float(seuil_str) if seuil_str else 100.0
    
    produits = Produit.get_stock_faible(seuil)
    
    if not produits:
        print(f"\n✅ Aucun produit en dessous du seuil de {seuil}")
        return
    
    donnees = []
    for produit in produits:
        donnees.append({
            'Produit': produit.nom,
            'Stock Actuel': f"{produit.quantite} {produit.unite}",
            'Seuil': f"{seuil} {produit.unite}",
            'Statut': '🔴 CRITIQUE' if produit.quantite < seuil/2 else '🟡 FAIBLE'
        })
    
    afficher_tableau(donnees, ['Produit', 'Stock Actuel', 'Seuil', 'Statut'], 
                    "STOCKS FAIBLES")

def menu_employes(self):
    """Menu de gestion des employés"""
    options = [
        "Ajouter un employé",
        "Lister tous les employés",
        "Modifier un employé",
        "Supprimer un employé",
        "Calculer les salaires",
        "Voir la masse salariale",
        "Retour au menu principal"
    ]
    
    choix = saisir_choix(options, "=== GESTION DES EMPLOYÉS ===")
    
    if choix == 0:
        self.ajouter_employe()
    elif choix == 1:
        self.lister_employes()
    elif choix == 2:
        self.modifier_employe()
    elif choix == 3:
        self.supprimer_employe()
    elif choix == 4:
        self.calculer_salaires()
    elif choix == 5:
        self.voir_masse_salariale()

def ajouter_employe(self):
    """Ajoute un nouveau employé"""
    print("\n=== AJOUTER UN EMPLOYÉ ===")
    
    nom = input("Nom complet: ").strip()
    if not nom:
        print("❌ Le nom est obligatoire")
        return
    
    cin = input("CIN: ").strip()
    if not cin:
        print("❌ La CIN est obligatoire")
        return
    
    if not valider_cin(cin):
        print("⚠️ Format de CIN invalide, mais enregistré quand même")
    
    # Vérifier si la CIN existe déjà
    employe_existant = Employe.get_by_cin(cin)
    if employe_existant:
        print("❌ Un employé avec cette CIN existe déjà")
        return
    
    numero_cnss = input("Numéro CNSS (optionnel): ").strip()
    
    salaire_str = input("Salaire brut mensuel: ").strip()
    salaire_brut = valider_montant(salaire_str)
    if salaire_brut is None:
        return
    
    employe = Employe(
        nom=nom,
        cin=cin,
        numero_cnss=numero_cnss,
        salaire_brut=salaire_brut
    )
    
    # Calculer et afficher le détail du salaire
    calcul = employe.calculer_salaire()
    print(f"\n📊 DÉTAIL DU SALAIRE:")
    print(f"   • Salaire brut: {formater_montant(calcul['salaire_brut'])}")
    print(f"   • CNSS: {formater_montant(calcul['cnss'])}")
    print(f"   • AMO: {formater_montant(calcul['amo'])}")
    print(f"   • IR: {formater_montant(calcul['ir'])}")
    print(f"   • Total déductions: {formater_montant(calcul['total_deductions'])}")
    print(f"   • Salaire net: {formater_montant(calcul['salaire_net'])}")
    
    if confirmer_action("Confirmer l'ajout de cet employé ?"):
        employe.save()

def lister_employes(self):
    """Liste tous les employés"""
    employes = Employe.get_all()
    
    if not employes:
        print("\n📋 Aucun employé enregistré")
        return
    
    donnees = []
    for employe in employes:
        donnees.append({
            'ID': employe.id,
            'Nom': employe.nom,
            'CIN': employe.cin,
            'Salaire Brut': formater_montant(employe.salaire_brut),
            'Salaire Net': formater_montant(employe.salaire_net)
        })
    
    afficher_tableau(donnees, ['ID', 'Nom', 'CIN', 'Salaire Brut', 'Salaire Net'], 
                    "LISTE DES EMPLOYÉS")

def voir_masse_salariale(self):
    """Affiche la masse salariale totale"""
    masse = Employe.calculer_masse_salariale()
    
    print(f"\n💼 MASSE SALARIALE:")
    print(f"   • Nombre d'employés: {masse['nombre_employes']}")
    print(f"   • Total salaires bruts: {formater_montant(masse['total_brut'])}")
    print(f"   • Total salaires nets: {formater_montant(masse['total_net'])}")
    print(f"   • Total CNSS: {formater_montant(masse['total_cnss'])}")
    print(f"   • Total AMO: {formater_montant(masse['total_amo'])}")
    print(f"   • Total IR: {formater_montant(masse['total_ir'])}")
    print(f"   • Total charges: {formater_montant(masse['total_charges'])}")

def menu_transactions(self):
    """Menu de gestion des transactions"""
    options = [
        "Enregistrer une vente",
        "Enregistrer un achat",
        "Lister les transactions",
        "Voir les ventes du mois",
        "Voir les achats du mois",
        "Retour au menu principal"
    ]
    
    choix = saisir_choix(options, "=== GESTION DES TRANSACTIONS ===")
    
    if choix == 0:
        self.enregistrer_vente()
    elif choix == 1:
        self.enregistrer_achat()
    elif choix == 2:
        self.lister_transactions()
    elif choix == 3:
        self.voir_ventes_mois()
    elif choix == 4:
        self.voir_achats_mois()

def menu_rapports(self):
    """Menu des rapports et analyses"""
    options = [
        "Rapport mensuel",
        "Bilan annuel",
        "Déclarations fiscales",
        "Retour au menu principal"
    ]
    
    choix = saisir_choix(options, "=== RAPPORTS ET ANALYSES ===")
    
    if choix == 0:
        self.generer_rapport_mensuel()
    elif choix == 1:
        self.generer_bilan_annuel()
    elif choix == 2:
        self.voir_declarations_fiscales()

def generer_rapport_mensuel(self):
    """Génère un rapport mensuel"""
    mois = input("Mois (YYYY-MM, ex: 2024-01): ").strip()
    
    try:
        # Valider le format
        datetime.strptime(mois, "%Y-%m")
        afficher_rapport_mensuel(mois)
    except ValueError:
        print("❌ Format de mois invalide. Utilisez YYYY-MM")

def generer_bilan_annuel(self):
    """Génère un bilan annuel"""
    annee_str = input("Année (ex: 2024): ").strip()
    
    try:
        annee = int(annee_str)
        if annee < 2000 or annee > 2100:
            print("❌ Année invalide")
            return
        
        afficher_bilan_annuel(annee)
    except ValueError:
        print("❌ Format d'année invalide")

def menu_utilitaires(self):
    """Menu des utilitaires"""
    options = [
        "Sauvegarder la base de données",
        "Initialiser des données de test",
        "Voir les statistiques",
        "Retour au menu principal"
    ]
    
    choix = saisir_choix(options, "=== UTILITAIRES ===")
    
    if choix == 0:
        sauvegarder_backup()
    elif choix == 1:
        self.initialiser_donnees_test()
    elif choix == 2:
        self.voir_statistiques()

def initialiser_donnees_test(self):
    """Initialise des données de test"""
    if not confirmer_action("Créer des données de test ?"):
        return
    
    print("🔄 Création des données de test...")
    
    # Clients de test
    clients_test = [
        {"nom": "Station TOTAL Casablanca", "telephone": "0522123456", "adresse": "Bd Mohammed V, Casablanca"},
        {"nom": "Station AFRIQUIA Rabat", "telephone": "0537654321", "adresse": "Av Hassan II, Rabat"},
        {"nom": "Station SHELL Marrakech", "telephone": "0524987654", "adresse": "Route de Fès, Marrakech"}
    ]
    
    for client_data in clients_test:
        client = Client(**client_data)
        client.save()
    
    # Fournisseurs de test
    fournisseurs_test = [
        {"nom": "SAMIR Raffinerie", "telephone": "0523111222", "adresse": "Mohammedia"},
        {"nom": "Distributeur Carburants Maroc", "telephone": "0522333444", "adresse": "Casablanca"}
    ]
    
    for fournisseur_data in fournisseurs_test:
        fournisseur = Fournisseur(**fournisseur_data)
        fournisseur.save()
    
    # Produits de test
    produits_test = [
        {"nom": "Gasoil", "prix_achat": 12.50, "prix_vente": 14.20, "quantite": 5000},
        {"nom": "Essence Super", "prix_achat": 13.80, "prix_vente": 15.60, "quantite": 3000},
        {"nom": "Essence Sans Plomb", "prix_achat": 13.20, "prix_vente": 15.00, "quantite": 2500}
    ]
    
    for produit_data in produits_test:
        produit = Produit(**produit_data)
        produit.save()
    
    # Employés de test
    employes_test = [
        {"nom": "Ahmed BENALI", "cin": "AB123456", "numero_cnss": "1234567890", "salaire_brut": 8000},
        {"nom": "Fatima ALAOUI", "cin": "FA654321", "numero_cnss": "0987654321", "salaire_brut": 6500},
        {"nom": "Mohamed TAZI", "cin": "MT789012", "numero_cnss": "1122334455", "salaire_brut": 5500}
    ]
    
    for employe_data in employes_test:
        employe = Employe(**employe_data)
        employe.save()
    
    print("✅ Données de test créées avec succès!")

def voir_statistiques(self):
    """Affiche les statistiques générales"""
    nb_clients = len(Client.get_all())
    nb_fournisseurs = len(Fournisseur.get_all())
    nb_produits = len(Produit.get_all())
    nb_employes = len(Employe.get_all())
    nb_transactions = len(Transaction.get_all())
    
    print(f"\n📊 STATISTIQUES GÉNÉRALES:")
    print(f"   • Clients: {nb_clients}")
    print(f"   • Fournisseurs: {nb_fournisseurs}")
    print(f"   • Produits: {nb_produits}")
    print(f"   • Employés: {nb_employes}")
    print(f"   • Transactions: {nb_transactions}")
    
    if nb_produits > 0:
        produits = Produit.get_all()
        valeur_stock = sum(p.calculer_valeur_stock() for p in produits)
        print(f"   • Valeur totale du stock: {formater_montant(valeur_stock)}")
    
    if nb_employes > 0:
        masse = Employe.calculer_masse_salariale()
        print(f"   • Masse salariale mensuelle: {formater_montant(masse['total_brut'])}")
