#!/usr/bin/env python3
"""
Tests unitaires pour COMTABELITE
Validation des calculs fiscaux et sociaux
"""

import sys
import os
import unittest
from decimal import Decimal

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.calculs import calculer_salaire, calculer_ir
from models.employe import Employe
from models.produit import Produit
from models.transaction import Transaction
from config import *

class TestCalculsSalaires(unittest.TestCase):
    """Tests des calculs de salaires selon la législation marocaine"""
    
    def test_calcul_salaire_8000(self):
        """Test calcul salaire 8000 DH"""
        resultat = calculer_salaire(8000.0)
        
        # Vérifications selon la législation marocaine
        self.assertAlmostEqual(resultat['cnss'], 268.80, places=2)  # 4.48% de 6000 (plafonné)
        self.assertAlmostEqual(resultat['amo'], 113.00, places=2)   # 2.26% de 5000 (plafonné)
        self.assertAlmostEqual(resultat['ir'], 1156.85, places=2)   # Selon barème IR
        self.assertAlmostEqual(resultat['salaire_net'], 6461.35, places=2)
    
    def test_calcul_salaire_3000(self):
        """Test calcul salaire 3000 DH (proche du SMIG)"""
        resultat = calculer_salaire(3000.0)
        
        # CNSS: 4.48% de 3000
        self.assertAlmostEqual(resultat['cnss'], 134.40, places=2)
        # AMO: 2.26% de 3000
        self.assertAlmostEqual(resultat['amo'], 67.80, places=2)
        # IR: exonéré (salaire imposable < 2500 après déductions)
        self.assertAlmostEqual(resultat['ir'], 0.0, places=2)
    
    def test_calcul_salaire_15000(self):
        """Test calcul salaire élevé 15000 DH"""
        resultat = calculer_salaire(15000.0)
        
        # CNSS plafonné à 6000
        self.assertAlmostEqual(resultat['cnss'], 268.80, places=2)
        # AMO plafonné à 5000
        self.assertAlmostEqual(resultat['amo'], 113.00, places=2)
        # IR élevé
        self.assertGreater(resultat['ir'], 3000.0)
        # Salaire net
        self.assertLess(resultat['salaire_net'], resultat['salaire_brut'])
    
    def test_calcul_ir_tranches(self):
        """Test du calcul IR par tranches"""
        # Test tranche exonérée
        ir1 = calculer_ir(3000, 134.40, 67.80)  # Salaire imposable = 2797.80
        self.assertAlmostEqual(ir1, 29.78, places=2)
        
        # Test tranche 10%
        ir2 = calculer_ir(5000, 224.0, 113.0)   # Salaire imposable = 4663
        self.assertGreater(ir2, 200.0)
        self.assertLess(ir2, 500.0)

class TestCalculsProduits(unittest.TestCase):
    """Tests des calculs liés aux produits"""
    
    def test_calcul_marge(self):
        """Test calcul de marge"""
        produit = Produit(nom="Test", prix_achat=10.0, prix_vente=12.0, quantite=100)
        marge = produit.calculer_marge()
        self.assertAlmostEqual(marge, 20.0, places=1)  # (12-10)/10 * 100 = 20%
    
    def test_valeur_stock(self):
        """Test calcul valeur stock"""
        produit = Produit(nom="Test", prix_achat=15.0, prix_vente=18.0, quantite=200)
        valeur = produit.calculer_valeur_stock()
        self.assertAlmostEqual(valeur, 3000.0, places=2)  # 15 * 200 = 3000

class TestValidations(unittest.TestCase):
    """Tests des validations"""
    
    def test_validation_salaire(self):
        """Test validation des salaires"""
        self.assertTrue(valider_salaire(5000.0))
        self.assertFalse(valider_salaire(1000.0))  # Trop bas
        self.assertFalse(valider_salaire(200000.0))  # Trop élevé
    
    def test_taux_is(self):
        """Test calcul taux IS selon CA"""
        # PME
        taux_pme = get_taux_is(2000000.0)
        self.assertEqual(taux_pme, TAUX_IS_PME)
        
        # Grande entreprise
        taux_standard = get_taux_is(5000000.0)
        self.assertEqual(taux_standard, TAUX_IS_STANDARD)
    
    def test_taux_tva(self):
        """Test taux TVA"""
        self.assertEqual(get_taux_tva("standard"), 0.20)
        self.assertEqual(get_taux_tva("reduit"), 0.10)
        self.assertEqual(get_taux_tva("super_reduit"), 0.07)

class TestTransactions(unittest.TestCase):
    """Tests des transactions"""
    
    def test_calcul_tva_transaction(self):
        """Test calcul TVA dans une transaction"""
        # Simulation d'une vente de 1000L à 14.20 DH/L
        quantite = 1000.0
        prix_unitaire = 14.20
        taux_tva = 0.20
        
        total_ht = quantite * prix_unitaire  # 14200
        tva = total_ht * taux_tva           # 2840
        total_ttc = total_ht + tva          # 17040
        
        self.assertAlmostEqual(total_ht, 14200.0, places=2)
        self.assertAlmostEqual(tva, 2840.0, places=2)
        self.assertAlmostEqual(total_ttc, 17040.0, places=2)

class TestFormats(unittest.TestCase):
    """Tests des formats et utilitaires"""
    
    def test_format_devise(self):
        """Test formatage devise"""
        resultat = formater_devise(1234.56)
        self.assertEqual(resultat, "1,234.56 DH")
    
    def test_format_devise_entier(self):
        """Test formatage devise nombre entier"""
        resultat = formater_devise(1000.0)
        self.assertEqual(resultat, "1,000.00 DH")

class TestCoherence(unittest.TestCase):
    """Tests de cohérence globale"""
    
    def test_coherence_employe(self):
        """Test cohérence calculs employé"""
        employe = Employe(nom="Test", cin="T123456", salaire_brut=6000.0)
        calcul = employe.calculer_salaire()
        
        # Vérifications de cohérence
        self.assertEqual(calcul['salaire_brut'], 6000.0)
        self.assertGreater(calcul['salaire_net'], 0)
        self.assertLess(calcul['salaire_net'], calcul['salaire_brut'])
        self.assertGreater(calcul['total_deductions'], 0)
        self.assertAlmostEqual(
            calcul['salaire_net'] + calcul['total_deductions'],
            calcul['salaire_brut'],
            places=2
        )
    
    def test_plafonds_cotisations(self):
        """Test respect des plafonds de cotisations"""
        # Salaire très élevé pour tester les plafonds
        resultat = calculer_salaire(20000.0)
        
        # CNSS ne doit pas dépasser 4.48% de 6000
        cnss_max = 6000.0 * TAUX_CNSS_EMPLOYE
        self.assertAlmostEqual(resultat['cnss'], cnss_max, places=2)
        
        # AMO ne doit pas dépasser 2.26% de 5000
        amo_max = 5000.0 * TAUX_AMO_EMPLOYE
        self.assertAlmostEqual(resultat['amo'], amo_max, places=2)

def run_tests():
    """Lance tous les tests"""
    print("🧪 TESTS UNITAIRES COMTABELITE")
    print("=" * 40)
    
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les classes de tests
    suite.addTests(loader.loadTestsFromTestCase(TestCalculsSalaires))
    suite.addTests(loader.loadTestsFromTestCase(TestCalculsProduits))
    suite.addTests(loader.loadTestsFromTestCase(TestValidations))
    suite.addTests(loader.loadTestsFromTestCase(TestTransactions))
    suite.addTests(loader.loadTestsFromTestCase(TestFormats))
    suite.addTests(loader.loadTestsFromTestCase(TestCoherence))
    
    # Lancer les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Résumé
    print("\n" + "=" * 40)
    if result.wasSuccessful():
        print("✅ TOUS LES TESTS SONT PASSÉS!")
        print(f"Tests exécutés: {result.testsRun}")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ!")
        print(f"Tests exécutés: {result.testsRun}")
        print(f"Échecs: {len(result.failures)}")
        print(f"Erreurs: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
