# COMTABELITE - Système de Comptabilité pour Entreprise de Carburants

## 🚀 Description

COMTABELITE est un système de comptabilité complet développé en Python pour les entreprises marocaines spécialisées dans la vente de carburants. Le système respecte la législation fiscale et sociale marocaine et offre une interface en ligne de commande intuitive.

## ✨ Fonctionnalités Principales

### 📊 Gestion Complète
- **Clients** : Ajout, modification, suppression, suivi des soldes
- **Fournisseurs** : Gestion des fournisseurs et suivi des dettes
- **Produits** : Gestion des carburants avec calcul automatique des marges
- **Employés** : Gestion RH avec calculs de paie selon la législation marocaine
- **Transactions** : Enregistrement des achats et ventes avec mise à jour automatique des stocks

### 💰 Calculs Fiscaux et Sociaux
- **Calculs de salaires** selon la législation marocaine 2024 :
  - CNSS employé : 4.48% (plafonné à 6000 DH)
  - AMO employé : 2.26% (plafonné à 5000 DH)
  - IR selon le barème progressif marocain
- **TVA** : Calcul automatique à 20%
- **Déclarations fiscales** : TVA, IS, IR
- **Déclarations sociales** : CNSS, AMO, IR par employé

### 📈 Rapports et Analyses
- **Rapports mensuels** complets
- **Bilans annuels** avec analyse de croissance
- **Suivi des stocks** et alertes de stock faible
- **Analyse de rentabilité** par produit
- **Statistiques générales** de l'entreprise

## 🏗️ Architecture du Projet

```
COMTABELITE/
├── models/                 # Modèles de données
│   ├── __init__.py
│   ├── database.py        # Configuration SQLite
│   ├── client.py          # Modèle Client
│   ├── fournisseur.py     # Modèle Fournisseur
│   ├── produit.py         # Modèle Produit
│   ├── transaction.py     # Modèle Transaction
│   ├── employe.py         # Modèle Employé
│   └── declaration.py     # Déclarations fiscales/sociales
├── utils/                 # Utilitaires
│   ├── __init__.py
│   ├── calculs.py         # Calculs fiscaux et sociaux
│   ├── rapports.py        # Génération de rapports
│   └── helpers.py         # Fonctions utilitaires
├── data/                  # Base de données
│   └── fuel_company.db    # Base SQLite
├── main.py               # Interface CLI principale
├── menus_supplementaires.py # Menus additionnels
├── requirements.txt      # Dépendances Python
└── README.md            # Documentation
```

## 🛠️ Installation et Configuration

### Prérequis
- Python 3.11 ou plus récent
- SQLite (inclus avec Python)

### Installation
1. Clonez ou téléchargez le projet
2. Naviguez vers le dossier du projet
3. Installez les dépendances (optionnel, toutes les bibliothèques sont standard) :
   ```bash
   pip install -r requirements.txt
   ```

### Lancement
```bash
python main.py
```

## 📋 Guide d'Utilisation

### Premier Démarrage
1. Lancez l'application avec `python main.py`
2. La base de données sera créée automatiquement
3. Utilisez le menu "Utilitaires > Initialiser des données de test" pour créer des données d'exemple

### Navigation
- Utilisez les numéros pour naviguer dans les menus
- Suivez les instructions à l'écran
- Tapez 'o' pour confirmer les actions importantes

### Fonctionnalités Clés

#### Gestion des Transactions
1. **Ventes** : Menu Transactions > Enregistrer une vente
   - Sélectionnez le client et le produit
   - Saisissez la quantité
   - Le stock et le solde client sont mis à jour automatiquement

2. **Achats** : Menu Transactions > Enregistrer un achat
   - Sélectionnez le fournisseur et le produit
   - Le stock augmente, la dette fournisseur aussi

#### Calculs de Salaires
- Les salaires sont calculés automatiquement selon la législation marocaine
- Barème IR 2024 appliqué
- Cotisations CNSS et AMO avec plafonds respectés

#### Rapports
- **Rapport mensuel** : Vue d'ensemble d'un mois donné
- **Bilan annuel** : Analyse complète avec évolution mensuelle
- **Statistiques** : Vue rapide des données clés

## 💡 Exemples d'Utilisation

### Calcul de Salaire (Exemple)
Pour un salaire brut de 8000 DH :
- CNSS : 268.80 DH (4.48% de 6000 DH plafonné)
- AMO : 113.00 DH (2.26% de 5000 DH plafonné)
- IR : 1156.85 DH (selon barème progressif)
- **Salaire net : 6461.35 DH**

### Transaction de Vente (Exemple)
Vente de 1000L de Gasoil à 14.20 DH/L :
- Montant HT : 14,200.00 DH
- TVA (20%) : 2,840.00 DH
- **Total TTC : 17,040.00 DH**
- Stock réduit automatiquement
- Solde client crédité

## 🔧 Fonctionnalités Techniques

### Base de Données
- **SQLite** pour la persistance des données
- **7 tables principales** : clients, fournisseurs, produits, transactions, employés, déclarations_fiscales, declarations_sociales
- **Sauvegarde automatique** après chaque opération

### Validation des Données
- **CIN marocaine** : Format validé (1-2 lettres + 6-8 chiffres)
- **Téléphones** : Formats marocains acceptés (06/07/05, +212, 00212)
- **Montants** : Validation des saisies numériques
- **Dates** : Formats standardisés

### Calculs Conformes
- **Législation marocaine 2024** respectée
- **Taux officiels** : CNSS, AMO, IR, TVA
- **Plafonds** appliqués automatiquement

## 📊 Rapports Disponibles

### Rapport Mensuel
- Chiffre d'affaires HT/TTC
- TVA collectée/déductible
- Soldes clients/fournisseurs
- Masse salariale détaillée
- État des stocks
- Nombre de transactions

### Bilan Annuel
- Évolution mensuelle du CA
- Calcul de l'IS
- Analyse par produit
- Indicateurs de croissance
- Déclarations fiscales de l'année

## 🛡️ Sécurité et Sauvegarde

### Sauvegarde
- Menu "Utilitaires > Sauvegarder la base de données"
- Crée une copie horodatée de la base
- Recommandé avant les opérations importantes

### Validation
- Confirmations pour les suppressions
- Vérifications de cohérence des données
- Messages d'erreur explicites

## 🔄 Maintenance

### Mise à Jour des Taux
Les taux fiscaux et sociaux sont définis dans `utils/calculs.py` :
- Modifiez les constantes en début de fichier
- Relancez l'application pour appliquer les nouveaux taux

### Ajout de Fonctionnalités
- Modèles dans le dossier `models/`
- Calculs dans `utils/calculs.py`
- Interface dans `main.py`

## 📞 Support

Pour toute question ou amélioration :
- Consultez le code source commenté
- Vérifiez les messages d'erreur dans l'application
- Utilisez les données de test pour comprendre le fonctionnement

## 📄 Licence

Système développé pour les entreprises marocaines de carburants.
Respecte la législation fiscale et sociale du Maroc (2024).

---

**COMTABELITE v1.0** - Système de comptabilité professionnel pour entreprises de carburants 🚗⛽
