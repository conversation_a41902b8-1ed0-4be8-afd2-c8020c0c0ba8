"""
Modèle Fournisseur pour le système de comptabilité COMTABELITE
"""

from typing import List, Optional, Dict, Any
from .database import db

class Fournisseur:
    """Modèle pour gérer les fournisseurs de l'entreprise"""
    
    def __init__(self, id: Optional[int] = None, nom: str = "", telephone: str = "", 
                 adresse: str = "", solde: float = 0.0):
        """
        Initialise un fournisseur
        
        Args:
            id: Identifiant unique du fournisseur
            nom: Nom du fournisseur
            telephone: Numéro de téléphone
            adresse: Adresse du fournisseur
            solde: Solde du compte fournisseur (négatif = dette)
        """
        self.id = id
        self.nom = nom
        self.telephone = telephone
        self.adresse = adresse
        self.solde = solde
    
    def save(self) -> int:
        """
        Sauvegarde le fournisseur dans la base de données
        
        Returns:
            ID du fournisseur créé ou modifié
        """
        if self.id is None:
            # Nouveau fournisseur
            cursor = db.execute_query(
                """INSERT INTO fournisseurs (nom, telephone, adresse, solde) 
                   VALUES (?, ?, ?, ?)""",
                (self.nom, self.telephone, self.adresse, self.solde)
            )
            self.id = cursor.lastrowid
            db.commit()
            print(f"✅ Fournisseur '{self.nom}' créé avec l'ID {self.id}")
        else:
            # Mise à jour fournisseur existant
            db.execute_query(
                """UPDATE fournisseurs SET nom=?, telephone=?, adresse=?, solde=? 
                   WHERE id=?""",
                (self.nom, self.telephone, self.adresse, self.solde, self.id)
            )
            db.commit()
            print(f"✅ Fournisseur '{self.nom}' mis à jour")
        
        return self.id
    
    def delete(self) -> bool:
        """
        Supprime le fournisseur de la base de données
        
        Returns:
            True si suppression réussie, False sinon
        """
        if self.id is None:
            print("❌ Impossible de supprimer un fournisseur sans ID")
            return False
        
        cursor = db.execute_query("DELETE FROM fournisseurs WHERE id=?", (self.id,))
        db.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ Fournisseur '{self.nom}' supprimé")
            return True
        else:
            print(f"❌ Fournisseur avec l'ID {self.id} non trouvé")
            return False
    
    def mettre_a_jour_solde(self, montant: float, operation: str = "ajouter"):
        """
        Met à jour le solde du fournisseur
        
        Args:
            montant: Montant à ajouter ou soustraire
            operation: "ajouter" ou "soustraire"
        """
        if operation == "ajouter":
            self.solde += montant
        elif operation == "soustraire":
            self.solde -= montant
        
        if self.id:
            db.execute_query(
                "UPDATE fournisseurs SET solde=? WHERE id=?",
                (self.solde, self.id)
            )
            db.commit()
    
    @classmethod
    def get_by_id(cls, fournisseur_id: int) -> Optional['Fournisseur']:
        """
        Récupère un fournisseur par son ID
        
        Args:
            fournisseur_id: ID du fournisseur à récupérer
            
        Returns:
            Instance Fournisseur ou None si non trouvé
        """
        cursor = db.execute_query("SELECT * FROM fournisseurs WHERE id=?", (fournisseur_id,))
        row = cursor.fetchone()
        
        if row:
            return cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            )
        return None
    
    @classmethod
    def get_all(cls) -> List['Fournisseur']:
        """
        Récupère tous les fournisseurs
        
        Returns:
            Liste de tous les fournisseurs
        """
        cursor = db.execute_query("SELECT * FROM fournisseurs ORDER BY nom")
        fournisseurs = []
        
        for row in cursor.fetchall():
            fournisseurs.append(cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            ))
        
        return fournisseurs
    
    @classmethod
    def search_by_name(cls, nom: str) -> List['Fournisseur']:
        """
        Recherche des fournisseurs par nom
        
        Args:
            nom: Nom ou partie du nom à rechercher
            
        Returns:
            Liste des fournisseurs correspondants
        """
        cursor = db.execute_query(
            "SELECT * FROM fournisseurs WHERE nom LIKE ? ORDER BY nom",
            (f"%{nom}%",)
        )
        fournisseurs = []
        
        for row in cursor.fetchall():
            fournisseurs.append(cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            ))
        
        return fournisseurs
    
    @classmethod
    def get_dettes(cls) -> List['Fournisseur']:
        """
        Récupère les fournisseurs avec des dettes (solde négatif)
        
        Returns:
            Liste des fournisseurs avec solde < 0
        """
        cursor = db.execute_query(
            "SELECT * FROM fournisseurs WHERE solde < 0 ORDER BY solde ASC"
        )
        fournisseurs = []
        
        for row in cursor.fetchall():
            fournisseurs.append(cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            ))
        
        return fournisseurs
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit le fournisseur en dictionnaire
        
        Returns:
            Dictionnaire représentant le fournisseur
        """
        return {
            'id': self.id,
            'nom': self.nom,
            'telephone': self.telephone,
            'adresse': self.adresse,
            'solde': self.solde
        }
    
    def __str__(self) -> str:
        """Représentation string du fournisseur"""
        return f"Fournisseur(ID: {self.id}, Nom: {self.nom}, Solde: {self.solde:.2f} DH)"
    
    def __repr__(self) -> str:
        """Représentation détaillée du fournisseur"""
        return (f"Fournisseur(id={self.id}, nom='{self.nom}', telephone='{self.telephone}', "
                f"adresse='{self.adresse}', solde={self.solde})")
