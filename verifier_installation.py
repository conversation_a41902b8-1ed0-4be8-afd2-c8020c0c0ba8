#!/usr/bin/env python3
"""
Script de vérification de l'installation COMTABELITE
Vérifie que tous les composants sont présents et fonctionnels
"""

import os
import sys
import sqlite3
from pathlib import Path

def verifier_structure_projet():
    """Vérifie la structure des dossiers et fichiers"""
    print("🔍 Vérification de la structure du projet...")
    
    fichiers_requis = [
        "main.py",
        "config.py", 
        "demo.py",
        "test_simple.py",
        "README.md",
        "PRESENTATION.md",
        "requirements.txt",
        "models/__init__.py",
        "models/database.py",
        "models/client.py",
        "models/fournisseur.py",
        "models/produit.py",
        "models/transaction.py",
        "models/employe.py",
        "models/declaration.py",
        "utils/__init__.py",
        "utils/calculs.py",
        "utils/rapports.py",
        "utils/helpers.py"
    ]
    
    dossiers_requis = [
        "models",
        "utils",
        "data"
    ]
    
    # Vérifier les dossiers
    for dossier in dossiers_requis:
        if os.path.exists(dossier):
            print(f"   ✅ Dossier {dossier}")
        else:
            print(f"   ❌ Dossier {dossier} manquant")
            return False
    
    # Vérifier les fichiers
    for fichier in fichiers_requis:
        if os.path.exists(fichier):
            print(f"   ✅ Fichier {fichier}")
        else:
            print(f"   ❌ Fichier {fichier} manquant")
            return False
    
    return True

def verifier_imports():
    """Vérifie que tous les imports fonctionnent"""
    print("\n📦 Vérification des imports...")
    
    try:
        # Imports principaux
        from models.database import db
        print("   ✅ models.database")
        
        from models.client import Client
        print("   ✅ models.client")
        
        from models.fournisseur import Fournisseur
        print("   ✅ models.fournisseur")
        
        from models.produit import Produit
        print("   ✅ models.produit")
        
        from models.transaction import Transaction
        print("   ✅ models.transaction")
        
        from models.employe import Employe
        print("   ✅ models.employe")
        
        from models.declaration import DeclarationFiscale, DeclarationSociale
        print("   ✅ models.declaration")
        
        from utils.calculs import calculer_salaire, declarer_impot
        print("   ✅ utils.calculs")
        
        from utils.rapports import generer_rapport_mensuel, generer_bilan_annuel
        print("   ✅ utils.rapports")
        
        from utils.helpers import formater_montant, valider_cin
        print("   ✅ utils.helpers")
        
        from config import CONFIG, TAUX_TVA_STANDARD
        print("   ✅ config")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Erreur d'import: {e}")
        return False

def verifier_base_donnees():
    """Vérifie la création et l'accès à la base de données"""
    print("\n🗄️ Vérification de la base de données...")
    
    try:
        from models.database import db
        
        # Créer les tables
        db.create_tables()
        print("   ✅ Création des tables")
        
        # Vérifier les tables
        conn = db.connect()
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        tables_requises = [
            'clients', 'fournisseurs', 'produits', 'transactions',
            'employes', 'declarations_fiscales', 'declarations_sociales'
        ]
        
        for table in tables_requises:
            if table in tables:
                print(f"   ✅ Table {table}")
            else:
                print(f"   ❌ Table {table} manquante")
                return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
        return False

def verifier_calculs():
    """Vérifie les calculs fiscaux et sociaux"""
    print("\n🧮 Vérification des calculs...")
    
    try:
        from utils.calculs import calculer_salaire
        from models.produit import Produit
        from config import formater_devise
        
        # Test calcul salaire
        resultat = calculer_salaire(8000.0)
        if abs(resultat['cnss'] - 268.80) < 0.01:
            print("   ✅ Calcul CNSS")
        else:
            print(f"   ❌ Calcul CNSS incorrect: {resultat['cnss']}")
            return False
        
        if abs(resultat['amo'] - 113.00) < 0.01:
            print("   ✅ Calcul AMO")
        else:
            print(f"   ❌ Calcul AMO incorrect: {resultat['amo']}")
            return False
        
        if resultat['salaire_net'] > 6000:
            print("   ✅ Calcul salaire net")
        else:
            print(f"   ❌ Salaire net incorrect: {resultat['salaire_net']}")
            return False
        
        # Test calcul produit
        produit = Produit(nom="Test", prix_achat=10.0, prix_vente=12.0, quantite=100)
        marge = produit.calculer_marge()
        if abs(marge - 20.0) < 0.1:
            print("   ✅ Calcul marge")
        else:
            print(f"   ❌ Calcul marge incorrect: {marge}")
            return False
        
        # Test formatage
        montant_formate = formater_devise(1234.56)
        if montant_formate == "1,234.56 DH":
            print("   ✅ Formatage montant")
        else:
            print(f"   ❌ Formatage incorrect: {montant_formate}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur calculs: {e}")
        return False

def verifier_interface():
    """Vérifie que l'interface principale peut démarrer"""
    print("\n🖥️ Vérification de l'interface...")
    
    try:
        # Import de l'application principale
        import main
        print("   ✅ Import main.py")
        
        # Vérifier que la classe existe
        app = main.ComptabiliteApp()
        print("   ✅ Classe ComptabiliteApp")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur interface: {e}")
        return False

def verifier_configuration():
    """Vérifie la configuration"""
    print("\n⚙️ Vérification de la configuration...")
    
    try:
        from config import CONFIG, TAUX_CNSS_EMPLOYE, TAUX_AMO_EMPLOYE, TAUX_TVA_STANDARD
        
        # Vérifier les taux
        if TAUX_CNSS_EMPLOYE == 0.0448:
            print("   ✅ Taux CNSS employé")
        else:
            print(f"   ❌ Taux CNSS incorrect: {TAUX_CNSS_EMPLOYE}")
            return False
        
        if TAUX_AMO_EMPLOYE == 0.0226:
            print("   ✅ Taux AMO employé")
        else:
            print(f"   ❌ Taux AMO incorrect: {TAUX_AMO_EMPLOYE}")
            return False
        
        if TAUX_TVA_STANDARD == 0.20:
            print("   ✅ Taux TVA")
        else:
            print(f"   ❌ Taux TVA incorrect: {TAUX_TVA_STANDARD}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur configuration: {e}")
        return False

def main():
    """Fonction principale de vérification"""
    print("🚀 VÉRIFICATION DE L'INSTALLATION COMTABELITE")
    print("=" * 55)
    
    # Liste des vérifications
    verifications = [
        ("Structure du projet", verifier_structure_projet),
        ("Imports Python", verifier_imports),
        ("Base de données", verifier_base_donnees),
        ("Calculs fiscaux", verifier_calculs),
        ("Interface utilisateur", verifier_interface),
        ("Configuration", verifier_configuration)
    ]
    
    resultats = []
    
    # Exécuter toutes les vérifications
    for nom, fonction in verifications:
        try:
            resultat = fonction()
            resultats.append((nom, resultat))
        except Exception as e:
            print(f"   ❌ Erreur lors de {nom}: {e}")
            resultats.append((nom, False))
    
    # Résumé final
    print("\n" + "=" * 55)
    print("📊 RÉSUMÉ DE LA VÉRIFICATION")
    print("=" * 55)
    
    succes = 0
    total = len(resultats)
    
    for nom, resultat in resultats:
        if resultat:
            print(f"✅ {nom}")
            succes += 1
        else:
            print(f"❌ {nom}")
    
    print(f"\n📈 Score: {succes}/{total} ({succes/total*100:.0f}%)")
    
    if succes == total:
        print("\n🎉 INSTALLATION PARFAITE!")
        print("✅ COMTABELITE est prêt à être utilisé")
        print("\n🚀 Pour démarrer:")
        print("   python main.py")
        print("\n📚 Pour la démonstration:")
        print("   python demo.py")
        print("\n🧪 Pour les tests:")
        print("   python test_simple.py")
        return True
    else:
        print("\n⚠️ PROBLÈMES DÉTECTÉS!")
        print("❌ Certains composants ne fonctionnent pas correctement")
        print("📞 Vérifiez les erreurs ci-dessus")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
