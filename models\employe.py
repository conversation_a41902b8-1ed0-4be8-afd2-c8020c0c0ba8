"""
Modèle Employé pour le système de comptabilité COMTABELITE
Gestion des employés et calculs de paie selon la législation marocaine
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from .database import db

class Employe:
    """Modèle pour gérer les employés de l'entreprise"""

    def __init__(self, id: Optional[int] = None, nom: str = "", cin: str = "",
                 numero_cnss: str = "", salaire_brut: float = 0.0, salaire_net: float = 0.0,
                 cnss: float = 0.0, amo: float = 0.0, ir: float = 0.0,
                 date_embauche: Optional[datetime] = None):
        """
        Initialise un employé

        Args:
            id: Identifiant unique de l'employé
            nom: Nom complet de l'employé
            cin: Carte d'identité nationale
            numero_cnss: Numéro CNSS
            salaire_brut: Salaire brut mensuel
            salaire_net: Salaire net calculé
            cnss: Cotisation CNSS
            amo: Cotisation AMO
            ir: Impôt sur le revenu
            date_embauche: Date d'embauche
        """
        self.id = id
        self.nom = nom
        self.cin = cin
        self.numero_cnss = numero_cnss
        self.salaire_brut = salaire_brut
        self.salaire_net = salaire_net
        self.cnss = cnss
        self.amo = amo
        self.ir = ir
        self.date_embauche = date_embauche or datetime.now()

    def calculer_salaire(self) -> Dict[str, float]:
        """
        Calcule le salaire net et les déductions selon la législation marocaine

        Returns:
            Dictionnaire avec les détails du calcul
        """
        # Taux de cotisations au Maroc (2024)
        TAUX_CNSS_EMPLOYE = 0.0448  # 4.48%
        TAUX_AMO_EMPLOYE = 0.0226   # 2.26%

        # Calcul CNSS (plafonné à 6000 DH)
        base_cnss = min(self.salaire_brut, 6000)
        self.cnss = base_cnss * TAUX_CNSS_EMPLOYE

        # Calcul AMO (plafonné à 5000 DH)
        base_amo = min(self.salaire_brut, 5000)
        self.amo = base_amo * TAUX_AMO_EMPLOYE

        # Calcul IR (Impôt sur le Revenu)
        self.ir = self._calculer_ir()

        # Salaire net
        self.salaire_net = self.salaire_brut - self.cnss - self.amo - self.ir

        return {
            'salaire_brut': self.salaire_brut,
            'cnss': self.cnss,
            'amo': self.amo,
            'ir': self.ir,
            'total_deductions': self.cnss + self.amo + self.ir,
            'salaire_net': self.salaire_net
        }

    def _calculer_ir(self) -> float:
        """
        Calcule l'impôt sur le revenu selon le barème marocain

        Returns:
            Montant de l'IR
        """
        # Salaire imposable = Salaire brut - CNSS - AMO
        salaire_imposable = self.salaire_brut - self.cnss - self.amo

        # Barème IR 2024 (mensuel)
        if salaire_imposable <= 2500:
            return 0
        elif salaire_imposable <= 4166.67:
            return (salaire_imposable - 2500) * 0.10
        elif salaire_imposable <= 5000:
            return 166.67 + (salaire_imposable - 4166.67) * 0.20
        elif salaire_imposable <= 6666.67:
            return 333.33 + (salaire_imposable - 5000) * 0.30
        elif salaire_imposable <= 15000:
            return 833.33 + (salaire_imposable - 6666.67) * 0.34
        else:
            return 3666.67 + (salaire_imposable - 15000) * 0.38

    def save(self) -> int:
        """
        Sauvegarde l'employé dans la base de données

        Returns:
            ID de l'employé créé ou modifié
        """
        # Calculer le salaire avant sauvegarde
        self.calculer_salaire()

        if self.id is None:
            # Nouvel employé
            cursor = db.execute_query(
                """INSERT INTO employes
                   (nom, cin, numero_cnss, salaire_brut, salaire_net, cnss, amo, ir, date_embauche)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (self.nom, self.cin, self.numero_cnss, self.salaire_brut, self.salaire_net,
                 self.cnss, self.amo, self.ir, self.date_embauche)
            )
            self.id = cursor.lastrowid
            db.commit()
            print(f"✅ Employé '{self.nom}' créé avec l'ID {self.id}")
        else:
            # Mise à jour employé existant
            db.execute_query(
                """UPDATE employes SET nom=?, cin=?, numero_cnss=?, salaire_brut=?,
                   salaire_net=?, cnss=?, amo=?, ir=?, date_embauche=? WHERE id=?""",
                (self.nom, self.cin, self.numero_cnss, self.salaire_brut, self.salaire_net,
                 self.cnss, self.amo, self.ir, self.date_embauche, self.id)
            )
            db.commit()
            print(f"✅ Employé '{self.nom}' mis à jour")

        return self.id

    def delete(self) -> bool:
        """
        Supprime l'employé de la base de données

        Returns:
            True si suppression réussie, False sinon
        """
        if self.id is None:
            print("❌ Impossible de supprimer un employé sans ID")
            return False

        cursor = db.execute_query("DELETE FROM employes WHERE id=?", (self.id,))
        db.commit()

        if cursor.rowcount > 0:
            print(f"✅ Employé '{self.nom}' supprimé")
            return True
        else:
            print(f"❌ Employé avec l'ID {self.id} non trouvé")
            return False

    @classmethod
    def get_by_id(cls, employe_id: int) -> Optional['Employe']:
        """
        Récupère un employé par son ID

        Args:
            employe_id: ID de l'employé à récupérer

        Returns:
            Instance Employe ou None si non trouvé
        """
        cursor = db.execute_query("SELECT * FROM employes WHERE id=?", (employe_id,))
        row = cursor.fetchone()

        if row:
            return cls(
                id=row['id'],
                nom=row['nom'],
                cin=row['cin'],
                numero_cnss=row['numero_cnss'],
                salaire_brut=row['salaire_brut'],
                salaire_net=row['salaire_net'],
                cnss=row['cnss'],
                amo=row['amo'],
                ir=row['ir'],
                date_embauche=datetime.fromisoformat(row['date_embauche'])
            )
        return None

    @classmethod
    def get_all(cls) -> List['Employe']:
        """
        Récupère tous les employés

        Returns:
            Liste de tous les employés
        """
        cursor = db.execute_query("SELECT * FROM employes ORDER BY nom")
        employes = []

        for row in cursor.fetchall():
            employes.append(cls(
                id=row['id'],
                nom=row['nom'],
                cin=row['cin'],
                numero_cnss=row['numero_cnss'],
                salaire_brut=row['salaire_brut'],
                salaire_net=row['salaire_net'],
                cnss=row['cnss'],
                amo=row['amo'],
                ir=row['ir'],
                date_embauche=datetime.fromisoformat(row['date_embauche'])
            ))

        return employes

    @classmethod
    def search_by_name(cls, nom: str) -> List['Employe']:
        """
        Recherche des employés par nom

        Args:
            nom: Nom ou partie du nom à rechercher

        Returns:
            Liste des employés correspondants
        """
        cursor = db.execute_query(
            "SELECT * FROM employes WHERE nom LIKE ? ORDER BY nom",
            (f"%{nom}%",)
        )
        employes = []

        for row in cursor.fetchall():
            employes.append(cls(
                id=row['id'],
                nom=row['nom'],
                cin=row['cin'],
                numero_cnss=row['numero_cnss'],
                salaire_brut=row['salaire_brut'],
                salaire_net=row['salaire_net'],
                cnss=row['cnss'],
                amo=row['amo'],
                ir=row['ir'],
                date_embauche=datetime.fromisoformat(row['date_embauche'])
            ))

        return employes

    @classmethod
    def get_by_cin(cls, cin: str) -> Optional['Employe']:
        """
        Récupère un employé par son CIN

        Args:
            cin: CIN de l'employé

        Returns:
            Instance Employe ou None si non trouvé
        """
        cursor = db.execute_query("SELECT * FROM employes WHERE cin=?", (cin,))
        row = cursor.fetchone()

        if row:
            return cls(
                id=row['id'],
                nom=row['nom'],
                cin=row['cin'],
                numero_cnss=row['numero_cnss'],
                salaire_brut=row['salaire_brut'],
                salaire_net=row['salaire_net'],
                cnss=row['cnss'],
                amo=row['amo'],
                ir=row['ir'],
                date_embauche=datetime.fromisoformat(row['date_embauche'])
            )
        return None

    @classmethod
    def calculer_masse_salariale(cls) -> Dict[str, float]:
        """
        Calcule la masse salariale totale de l'entreprise

        Returns:
            Dictionnaire avec les totaux
        """
        employes = cls.get_all()

        total_brut = sum(emp.salaire_brut for emp in employes)
        total_net = sum(emp.salaire_net for emp in employes)
        total_cnss = sum(emp.cnss for emp in employes)
        total_amo = sum(emp.amo for emp in employes)
        total_ir = sum(emp.ir for emp in employes)

        return {
            'nombre_employes': len(employes),
            'total_brut': total_brut,
            'total_net': total_net,
            'total_cnss': total_cnss,
            'total_amo': total_amo,
            'total_ir': total_ir,
            'total_charges': total_cnss + total_amo + total_ir
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit l'employé en dictionnaire

        Returns:
            Dictionnaire représentant l'employé
        """
        return {
            'id': self.id,
            'nom': self.nom,
            'cin': self.cin,
            'numero_cnss': self.numero_cnss,
            'salaire_brut': self.salaire_brut,
            'salaire_net': self.salaire_net,
            'cnss': self.cnss,
            'amo': self.amo,
            'ir': self.ir,
            'date_embauche': self.date_embauche.isoformat()
        }

    def __str__(self) -> str:
        """Représentation string de l'employé"""
        return f"Employé(ID: {self.id}, Nom: {self.nom}, Salaire Net: {self.salaire_net:.2f} DH)"

    def __repr__(self) -> str:
        """Représentation détaillée de l'employé"""
        return (f"Employe(id={self.id}, nom='{self.nom}', cin='{self.cin}', "
                f"salaire_brut={self.salaire_brut}, salaire_net={self.salaire_net})")