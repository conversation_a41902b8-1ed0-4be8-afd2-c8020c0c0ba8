"""
Modèles pour les déclarations fiscales et sociales - COMTABELITE
Gestion des déclarations TVA, IS, IR et déclarations sociales
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from .database import db

class DeclarationFiscale:
    """Modèle pour gérer les déclarations fiscales (TVA, IS, IR)"""

    def __init__(self, id: Optional[int] = None, type_impot: str = "", periode: str = "",
                 montant: float = 0.0, date_declaration: Optional[datetime] = None):
        """
        Initialise une déclaration fiscale

        Args:
            id: Identifiant unique de la déclaration
            type_impot: Type d'impôt ('TVA', 'IS', 'IR')
            periode: Période de la déclaration (ex: '2024-01', '2024')
            montant: Montant de l'impôt
            date_declaration: Date de la déclaration
        """
        self.id = id
        self.type_impot = type_impot
        self.periode = periode
        self.montant = montant
        self.date_declaration = date_declaration or datetime.now()

    def save(self) -> int:
        """
        Sauvegarde la déclaration dans la base de données

        Returns:
            ID de la déclaration créée ou modifiée
        """
        if self.id is None:
            # Nouvelle déclaration
            cursor = db.execute_query(
                """INSERT INTO declarations_fiscales (type_impot, periode, montant, date_declaration)
                   VALUES (?, ?, ?, ?)""",
                (self.type_impot, self.periode, self.montant, self.date_declaration)
            )
            self.id = cursor.lastrowid
            db.commit()
            print(f"✅ Déclaration {self.type_impot} pour {self.periode} créée avec l'ID {self.id}")
        else:
            # Mise à jour déclaration existante
            db.execute_query(
                """UPDATE declarations_fiscales SET type_impot=?, periode=?, montant=?, date_declaration=?
                   WHERE id=?""",
                (self.type_impot, self.periode, self.montant, self.date_declaration, self.id)
            )
            db.commit()
            print(f"✅ Déclaration {self.type_impot} pour {self.periode} mise à jour")

        return self.id

    def delete(self) -> bool:
        """
        Supprime la déclaration de la base de données

        Returns:
            True si suppression réussie, False sinon
        """
        if self.id is None:
            print("❌ Impossible de supprimer une déclaration sans ID")
            return False

        cursor = db.execute_query("DELETE FROM declarations_fiscales WHERE id=?", (self.id,))
        db.commit()

        if cursor.rowcount > 0:
            print(f"✅ Déclaration {self.type_impot} supprimée")
            return True
        else:
            print(f"❌ Déclaration avec l'ID {self.id} non trouvée")
            return False

    @classmethod
    def declarer_impot(cls, type_impot: str, periode: str, montant: float) -> 'DeclarationFiscale':
        """
        Crée et sauvegarde une nouvelle déclaration fiscale

        Args:
            type_impot: Type d'impôt ('TVA', 'IS', 'IR')
            periode: Période de la déclaration
            montant: Montant de l'impôt

        Returns:
            Instance de la déclaration créée
        """
        declaration = cls(
            type_impot=type_impot,
            periode=periode,
            montant=montant
        )
        declaration.save()
        return declaration

    @classmethod
    def get_by_id(cls, declaration_id: int) -> Optional['DeclarationFiscale']:
        """
        Récupère une déclaration par son ID

        Args:
            declaration_id: ID de la déclaration à récupérer

        Returns:
            Instance DeclarationFiscale ou None si non trouvée
        """
        cursor = db.execute_query("SELECT * FROM declarations_fiscales WHERE id=?", (declaration_id,))
        row = cursor.fetchone()

        if row:
            return cls(
                id=row['id'],
                type_impot=row['type_impot'],
                periode=row['periode'],
                montant=row['montant'],
                date_declaration=datetime.fromisoformat(row['date_declaration'])
            )
        return None

    @classmethod
    def get_all(cls) -> List['DeclarationFiscale']:
        """
        Récupère toutes les déclarations fiscales

        Returns:
            Liste de toutes les déclarations
        """
        cursor = db.execute_query("SELECT * FROM declarations_fiscales ORDER BY date_declaration DESC")
        declarations = []

        for row in cursor.fetchall():
            declarations.append(cls(
                id=row['id'],
                type_impot=row['type_impot'],
                periode=row['periode'],
                montant=row['montant'],
                date_declaration=datetime.fromisoformat(row['date_declaration'])
            ))

        return declarations

    @classmethod
    def get_by_type(cls, type_impot: str) -> List['DeclarationFiscale']:
        """
        Récupère les déclarations par type d'impôt

        Args:
            type_impot: Type d'impôt à rechercher

        Returns:
            Liste des déclarations du type spécifié
        """
        cursor = db.execute_query(
            "SELECT * FROM declarations_fiscales WHERE type_impot=? ORDER BY periode DESC",
            (type_impot,)
        )
        declarations = []

        for row in cursor.fetchall():
            declarations.append(cls(
                id=row['id'],
                type_impot=row['type_impot'],
                periode=row['periode'],
                montant=row['montant'],
                date_declaration=datetime.fromisoformat(row['date_declaration'])
            ))

        return declarations

    @classmethod
    def get_by_periode(cls, periode: str) -> List['DeclarationFiscale']:
        """
        Récupère les déclarations d'une période donnée

        Args:
            periode: Période à rechercher

        Returns:
            Liste des déclarations de la période
        """
        cursor = db.execute_query(
            "SELECT * FROM declarations_fiscales WHERE periode=? ORDER BY type_impot",
            (periode,)
        )
        declarations = []

        for row in cursor.fetchall():
            declarations.append(cls(
                id=row['id'],
                type_impot=row['type_impot'],
                periode=row['periode'],
                montant=row['montant'],
                date_declaration=datetime.fromisoformat(row['date_declaration'])
            ))

        return declarations

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit la déclaration en dictionnaire

        Returns:
            Dictionnaire représentant la déclaration
        """
        return {
            'id': self.id,
            'type_impot': self.type_impot,
            'periode': self.periode,
            'montant': self.montant,
            'date_declaration': self.date_declaration.isoformat()
        }

    def __str__(self) -> str:
        """Représentation string de la déclaration"""
        return f"Déclaration {self.type_impot}(Période: {self.periode}, Montant: {self.montant:.2f} DH)"

    def __repr__(self) -> str:
        """Représentation détaillée de la déclaration"""
        return (f"DeclarationFiscale(id={self.id}, type_impot='{self.type_impot}', "
                f"periode='{self.periode}', montant={self.montant})")


class DeclarationSociale:
    """Modèle pour gérer les déclarations sociales des employés"""

    def __init__(self, id: Optional[int] = None, employe_id: int = 0, mois: str = "",
                 cnss: float = 0.0, amo: float = 0.0, ir: float = 0.0, total: float = 0.0,
                 date_declaration: Optional[datetime] = None):
        """
        Initialise une déclaration sociale

        Args:
            id: Identifiant unique de la déclaration
            employe_id: ID de l'employé
            mois: Mois de la déclaration (ex: '2024-01')
            cnss: Montant CNSS
            amo: Montant AMO
            ir: Montant IR
            total: Total des cotisations
            date_declaration: Date de la déclaration
        """
        self.id = id
        self.employe_id = employe_id
        self.mois = mois
        self.cnss = cnss
        self.amo = amo
        self.ir = ir
        self.total = total
        self.date_declaration = date_declaration or datetime.now()

    def save(self) -> int:
        """
        Sauvegarde la déclaration sociale dans la base de données

        Returns:
            ID de la déclaration créée ou modifiée
        """
        # Calculer le total
        self.total = self.cnss + self.amo + self.ir

        if self.id is None:
            # Nouvelle déclaration
            cursor = db.execute_query(
                """INSERT INTO declarations_sociales
                   (employe_id, mois, cnss, amo, ir, total, date_declaration)
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (self.employe_id, self.mois, self.cnss, self.amo, self.ir, self.total, self.date_declaration)
            )
            self.id = cursor.lastrowid
            db.commit()
            print(f"✅ Déclaration sociale pour employé {self.employe_id} - {self.mois} créée")
        else:
            # Mise à jour déclaration existante
            db.execute_query(
                """UPDATE declarations_sociales SET employe_id=?, mois=?, cnss=?, amo=?, ir=?,
                   total=?, date_declaration=? WHERE id=?""",
                (self.employe_id, self.mois, self.cnss, self.amo, self.ir, self.total,
                 self.date_declaration, self.id)
            )
            db.commit()
            print(f"✅ Déclaration sociale mise à jour")

        return self.id

    @classmethod
    def get_by_id(cls, declaration_id: int) -> Optional['DeclarationSociale']:
        """
        Récupère une déclaration sociale par son ID

        Args:
            declaration_id: ID de la déclaration à récupérer

        Returns:
            Instance DeclarationSociale ou None si non trouvée
        """
        cursor = db.execute_query("SELECT * FROM declarations_sociales WHERE id=?", (declaration_id,))
        row = cursor.fetchone()

        if row:
            return cls(
                id=row['id'],
                employe_id=row['employe_id'],
                mois=row['mois'],
                cnss=row['cnss'],
                amo=row['amo'],
                ir=row['ir'],
                total=row['total'],
                date_declaration=datetime.fromisoformat(row['date_declaration'])
            )
        return None

    @classmethod
    def get_by_employe(cls, employe_id: int) -> List['DeclarationSociale']:
        """
        Récupère les déclarations sociales d'un employé

        Args:
            employe_id: ID de l'employé

        Returns:
            Liste des déclarations de l'employé
        """
        cursor = db.execute_query(
            "SELECT * FROM declarations_sociales WHERE employe_id=? ORDER BY mois DESC",
            (employe_id,)
        )
        declarations = []

        for row in cursor.fetchall():
            declarations.append(cls(
                id=row['id'],
                employe_id=row['employe_id'],
                mois=row['mois'],
                cnss=row['cnss'],
                amo=row['amo'],
                ir=row['ir'],
                total=row['total'],
                date_declaration=datetime.fromisoformat(row['date_declaration'])
            ))

        return declarations

    @classmethod
    def get_by_mois(cls, mois: str) -> List['DeclarationSociale']:
        """
        Récupère les déclarations sociales d'un mois donné

        Args:
            mois: Mois à rechercher (format: YYYY-MM)

        Returns:
            Liste des déclarations du mois
        """
        cursor = db.execute_query(
            "SELECT * FROM declarations_sociales WHERE mois=? ORDER BY employe_id",
            (mois,)
        )
        declarations = []

        for row in cursor.fetchall():
            declarations.append(cls(
                id=row['id'],
                employe_id=row['employe_id'],
                mois=row['mois'],
                cnss=row['cnss'],
                amo=row['amo'],
                ir=row['ir'],
                total=row['total'],
                date_declaration=datetime.fromisoformat(row['date_declaration'])
            ))

        return declarations

    @classmethod
    def calculer_totaux_mois(cls, mois: str) -> Dict[str, float]:
        """
        Calcule les totaux des cotisations pour un mois donné

        Args:
            mois: Mois à analyser

        Returns:
            Dictionnaire avec les totaux
        """
        declarations = cls.get_by_mois(mois)

        total_cnss = sum(decl.cnss for decl in declarations)
        total_amo = sum(decl.amo for decl in declarations)
        total_ir = sum(decl.ir for decl in declarations)

        return {
            'mois': mois,
            'nombre_employes': len(declarations),
            'total_cnss': total_cnss,
            'total_amo': total_amo,
            'total_ir': total_ir,
            'total_general': total_cnss + total_amo + total_ir
        }

    def get_employe_nom(self) -> str:
        """
        Récupère le nom de l'employé

        Returns:
            Nom de l'employé
        """
        from .employe import Employe
        employe = Employe.get_by_id(self.employe_id)
        return employe.nom if employe else "Employé inconnu"

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit la déclaration sociale en dictionnaire

        Returns:
            Dictionnaire représentant la déclaration
        """
        return {
            'id': self.id,
            'employe_id': self.employe_id,
            'employe_nom': self.get_employe_nom(),
            'mois': self.mois,
            'cnss': self.cnss,
            'amo': self.amo,
            'ir': self.ir,
            'total': self.total,
            'date_declaration': self.date_declaration.isoformat()
        }

    def __str__(self) -> str:
        """Représentation string de la déclaration sociale"""
        return f"Déclaration Sociale({self.get_employe_nom()}, {self.mois}, Total: {self.total:.2f} DH)"

    def __repr__(self) -> str:
        """Représentation détaillée de la déclaration sociale"""
        return (f"DeclarationSociale(id={self.id}, employe_id={self.employe_id}, "
                f"mois='{self.mois}', total={self.total})")