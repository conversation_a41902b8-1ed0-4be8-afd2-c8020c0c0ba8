"""
Fonctions utilitaires pour le système de comptabilité COMTABELITE
Helpers et fonctions d'assistance
"""

import re
from datetime import datetime
from typing import Optional, List, Dict, Any

def valider_cin(cin: str) -> bool:
    """
    Valide le format d'une CIN marocaine
    
    Args:
        cin: Numéro de CIN à valider
        
    Returns:
        True si le format est valide, False sinon
    """
    # Format CIN marocaine: 1-2 lettres suivies de 6-8 chiffres
    pattern = r'^[A-Za-z]{1,2}\d{6,8}$'
    return bool(re.match(pattern, cin.strip()))

def valider_telephone(telephone: str) -> bool:
    """
    Valide le format d'un numéro de téléphone marocain
    
    Args:
        telephone: Numéro de téléphone à valider
        
    Returns:
        True si le format est valide, False sinon
    """
    # Nettoyer le numéro
    tel_clean = re.sub(r'[\s\-\.]', '', telephone)
    
    # Formats acceptés:
    # 06XXXXXXXX, 07XXXXXXXX (mobiles)
    # 05XXXXXXXX (fixes)
    # +212XXXXXXXXX
    # 00212XXXXXXXXX
    patterns = [
        r'^0[567]\d{8}$',           # Format national
        r'^\+212[567]\d{8}$',       # Format international avec +
        r'^00212[567]\d{8}$'        # Format international avec 00
    ]
    
    return any(re.match(pattern, tel_clean) for pattern in patterns)

def formater_montant(montant: float, devise: str = "DH") -> str:
    """
    Formate un montant avec séparateurs de milliers
    
    Args:
        montant: Montant à formater
        devise: Devise à afficher
        
    Returns:
        Montant formaté
    """
    return f"{montant:,.2f} {devise}"

def formater_date(date: datetime, format: str = "%d/%m/%Y") -> str:
    """
    Formate une date selon le format spécifié
    
    Args:
        date: Date à formater
        format: Format de sortie
        
    Returns:
        Date formatée
    """
    return date.strftime(format)

def parser_periode(periode: str) -> tuple[datetime, datetime]:
    """
    Parse une période au format YYYY-MM et retourne les dates de début et fin
    
    Args:
        periode: Période au format YYYY-MM
        
    Returns:
        Tuple (date_debut, date_fin)
    """
    try:
        annee, mois = map(int, periode.split('-'))
        debut = datetime(annee, mois, 1)
        
        # Dernier jour du mois
        if mois == 12:
            fin = datetime(annee + 1, 1, 1)
        else:
            fin = datetime(annee, mois + 1, 1)
        
        # Soustraire un jour pour avoir le dernier jour du mois
        fin = fin.replace(day=fin.day - 1, hour=23, minute=59, second=59)
        
        return debut, fin
    except ValueError:
        raise ValueError(f"Format de période invalide: {periode}. Utilisez YYYY-MM")

def valider_montant(montant: str) -> Optional[float]:
    """
    Valide et convertit un montant saisi par l'utilisateur
    
    Args:
        montant: Montant sous forme de chaîne
        
    Returns:
        Montant en float ou None si invalide
    """
    try:
        # Remplacer la virgule par un point pour la conversion
        montant_clean = montant.replace(',', '.').strip()
        valeur = float(montant_clean)
        
        if valeur < 0:
            print("❌ Le montant ne peut pas être négatif")
            return None
        
        return valeur
    except ValueError:
        print("❌ Format de montant invalide")
        return None

def valider_quantite(quantite: str) -> Optional[float]:
    """
    Valide et convertit une quantité saisie par l'utilisateur
    
    Args:
        quantite: Quantité sous forme de chaîne
        
    Returns:
        Quantité en float ou None si invalide
    """
    try:
        quantite_clean = quantite.replace(',', '.').strip()
        valeur = float(quantite_clean)
        
        if valeur <= 0:
            print("❌ La quantité doit être positive")
            return None
        
        return valeur
    except ValueError:
        print("❌ Format de quantité invalide")
        return None

def saisir_choix(options: List[str], message: str = "Choisissez une option") -> int:
    """
    Affiche un menu et demande à l'utilisateur de choisir une option
    
    Args:
        options: Liste des options disponibles
        message: Message à afficher
        
    Returns:
        Index de l'option choisie (0-based)
    """
    print(f"\n{message}:")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")
    
    while True:
        try:
            choix = int(input("\nVotre choix: "))
            if 1 <= choix <= len(options):
                return choix - 1
            else:
                print(f"❌ Veuillez choisir un nombre entre 1 et {len(options)}")
        except ValueError:
            print("❌ Veuillez saisir un nombre valide")

def confirmer_action(message: str = "Êtes-vous sûr ?") -> bool:
    """
    Demande confirmation à l'utilisateur
    
    Args:
        message: Message de confirmation
        
    Returns:
        True si confirmé, False sinon
    """
    reponse = input(f"{message} (o/N): ").strip().lower()
    return reponse in ['o', 'oui', 'y', 'yes']

def afficher_tableau(donnees: List[Dict[str, Any]], colonnes: List[str], titre: str = ""):
    """
    Affiche des données sous forme de tableau
    
    Args:
        donnees: Liste de dictionnaires contenant les données
        colonnes: Liste des colonnes à afficher
        titre: Titre du tableau
    """
    if not donnees:
        print("Aucune donnée à afficher")
        return
    
    if titre:
        print(f"\n{titre}")
        print("=" * len(titre))
    
    # Calculer la largeur des colonnes
    largeurs = {}
    for col in colonnes:
        largeur_titre = len(col)
        largeur_max_donnees = max(len(str(row.get(col, ""))) for row in donnees)
        largeurs[col] = max(largeur_titre, largeur_max_donnees) + 2
    
    # Afficher l'en-tête
    ligne_entete = "|".join(col.center(largeurs[col]) for col in colonnes)
    print(ligne_entete)
    print("-" * len(ligne_entete))
    
    # Afficher les données
    for row in donnees:
        ligne = "|".join(str(row.get(col, "")).center(largeurs[col]) for col in colonnes)
        print(ligne)

def nettoyer_chaine(chaine: str) -> str:
    """
    Nettoie une chaîne de caractères (supprime espaces en trop, etc.)
    
    Args:
        chaine: Chaîne à nettoyer
        
    Returns:
        Chaîne nettoyée
    """
    return ' '.join(chaine.strip().split())

def generer_numero_cnss() -> str:
    """
    Génère un numéro CNSS factice pour les tests
    
    Returns:
        Numéro CNSS au format XXXXXXXXXX
    """
    import random
    return ''.join([str(random.randint(0, 9)) for _ in range(10)])

def calculer_age_entreprise(date_creation: datetime) -> int:
    """
    Calcule l'âge de l'entreprise en années
    
    Args:
        date_creation: Date de création de l'entreprise
        
    Returns:
        Âge en années
    """
    aujourd_hui = datetime.now()
    return aujourd_hui.year - date_creation.year

def formater_pourcentage(valeur: float, decimales: int = 1) -> str:
    """
    Formate un pourcentage
    
    Args:
        valeur: Valeur à formater (ex: 0.15 pour 15%)
        decimales: Nombre de décimales
        
    Returns:
        Pourcentage formaté
    """
    return f"{valeur:.{decimales}f}%"

def tronquer_texte(texte: str, longueur: int = 50) -> str:
    """
    Tronque un texte s'il est trop long
    
    Args:
        texte: Texte à tronquer
        longueur: Longueur maximale
        
    Returns:
        Texte tronqué avec "..." si nécessaire
    """
    if len(texte) <= longueur:
        return texte
    return texte[:longueur-3] + "..."

def sauvegarder_backup():
    """
    Crée une sauvegarde de la base de données
    """
    import shutil
    from datetime import datetime
    
    source = "data/fuel_company.db"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    destination = f"data/backup_fuel_company_{timestamp}.db"
    
    try:
        shutil.copy2(source, destination)
        print(f"✅ Sauvegarde créée: {destination}")
    except FileNotFoundError:
        print("❌ Fichier de base de données non trouvé")
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde: {e}")

def afficher_logo():
    """
    Affiche le logo de l'application
    """
    logo = """
    ╔═══════════════════════════════════════════════════════════╗
    ║                                                           ║
    ║    ██████╗ ██████╗ ███╗   ███╗████████╗ █████╗ ██████╗    ║
    ║   ██╔════╝██╔═══██╗████╗ ████║╚══██╔══╝██╔══██╗██╔══██╗   ║
    ║   ██║     ██║   ██║██╔████╔██║   ██║   ███████║██████╔╝   ║
    ║   ██║     ██║   ██║██║╚██╔╝██║   ██║   ██╔══██║██╔══██╗   ║
    ║   ╚██████╗╚██████╔╝██║ ╚═╝ ██║   ██║   ██║  ██║██████╔╝   ║
    ║    ╚═════╝ ╚═════╝ ╚═╝     ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═════╝    ║
    ║                                                           ║
    ║              SYSTÈME DE COMPTABILITÉ CARBURANTS          ║
    ║                     Version 1.0                          ║
    ║                                                           ║
    ╚═══════════════════════════════════════════════════════════╝
    """
    print(logo)
