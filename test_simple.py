#!/usr/bin/env python3
"""
Test simple des calculs COMTABELITE
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_calculs():
    """Test simple des calculs"""
    print("🧪 Test des calculs COMTABELITE")
    print("=" * 35)
    
    try:
        from utils.calculs import calculer_salaire
        print("✅ Import calculer_salaire OK")
        
        # Test calcul salaire 8000 DH
        resultat = calculer_salaire(8000.0)
        print(f"\n📊 Test salaire 8000 DH:")
        print(f"   • Salaire brut: {resultat['salaire_brut']:.2f} DH")
        print(f"   • CNSS: {resultat['cnss']:.2f} DH")
        print(f"   • AMO: {resultat['amo']:.2f} DH")
        print(f"   • IR: {resultat['ir']:.2f} DH")
        print(f"   • Salaire net: {resultat['salaire_net']:.2f} DH")
        
        # Vérifications
        assert abs(resultat['cnss'] - 268.80) < 0.01, f"CNSS incorrect: {resultat['cnss']}"
        assert abs(resultat['amo'] - 113.00) < 0.01, f"AMO incorrect: {resultat['amo']}"
        assert resultat['salaire_net'] > 6000, f"Salaire net trop bas: {resultat['salaire_net']}"
        
        print("✅ Calculs salaire OK")
        
    except Exception as e:
        print(f"❌ Erreur calculs salaire: {e}")
        return False
    
    try:
        from models.produit import Produit
        print("✅ Import Produit OK")
        
        # Test calcul marge
        produit = Produit(nom="Test", prix_achat=10.0, prix_vente=12.0, quantite=100)
        marge = produit.calculer_marge()
        valeur_stock = produit.calculer_valeur_stock()
        
        print(f"\n📦 Test produit:")
        print(f"   • Prix achat: {produit.prix_achat:.2f} DH")
        print(f"   • Prix vente: {produit.prix_vente:.2f} DH")
        print(f"   • Marge: {marge:.1f}%")
        print(f"   • Valeur stock: {valeur_stock:.2f} DH")
        
        assert abs(marge - 20.0) < 0.1, f"Marge incorrecte: {marge}"
        assert abs(valeur_stock - 1000.0) < 0.01, f"Valeur stock incorrecte: {valeur_stock}"
        
        print("✅ Calculs produit OK")
        
    except Exception as e:
        print(f"❌ Erreur calculs produit: {e}")
        return False
    
    try:
        from config import formater_devise, get_taux_is, TAUX_IS_PME, TAUX_IS_STANDARD
        print("✅ Import config OK")
        
        # Test formatage
        montant_formate = formater_devise(1234.56)
        print(f"\n💰 Test formatage: {montant_formate}")
        assert "1,234.56 DH" == montant_formate, f"Formatage incorrect: {montant_formate}"
        
        # Test taux IS
        taux_pme = get_taux_is(2000000.0)
        taux_standard = get_taux_is(5000000.0)
        print(f"   • Taux IS PME: {taux_pme*100:.0f}%")
        print(f"   • Taux IS standard: {taux_standard*100:.0f}%")
        
        assert taux_pme == TAUX_IS_PME, f"Taux IS PME incorrect: {taux_pme}"
        assert taux_standard == TAUX_IS_STANDARD, f"Taux IS standard incorrect: {taux_standard}"
        
        print("✅ Config OK")
        
    except Exception as e:
        print(f"❌ Erreur config: {e}")
        return False
    
    print("\n🎉 TOUS LES TESTS SONT PASSÉS!")
    return True

if __name__ == "__main__":
    success = test_calculs()
    if success:
        print("\n✅ Le système COMTABELITE fonctionne correctement!")
    else:
        print("\n❌ Des erreurs ont été détectées!")
    
    sys.exit(0 if success else 1)
