#!/usr/bin/env python3
"""
Script de lancement simplifié pour COMTABELITE
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def lancer_application():
    """Lance l'application COMTABELITE"""
    
    print("🚀 LANCEMENT DE COMTABELITE")
    print("=" * 40)
    
    try:
        # Vérifier les imports
        print("📦 Vérification des composants...")
        
        from models.database import db
        print("   ✅ Base de données")
        
        from main import ComptabiliteApp
        print("   ✅ Application principale")
        
        # Initialiser la base
        print("\n🗄️ Initialisation de la base de données...")
        db.create_tables()
        print("   ✅ Tables créées")
        
        # Lancer l'application
        print("\n🎯 Démarrage de l'interface...")
        print("=" * 40)
        
        app = ComptabiliteApp()
        app.demarrer()
        
    except KeyboardInterrupt:
        print("\n\n👋 Application fermée par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        print("\n🔧 Solutions possibles:")
        print("   • Vérifiez que Python 3.11+ est installé")
        print("   • Assurez-vous que tous les fichiers sont présents")
        print("   • Lancez: python verifier_installation.py")

if __name__ == "__main__":
    lancer_application()
