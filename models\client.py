"""
Modèle Client pour le système de comptabilité COMTABELITE
"""

from typing import List, Optional, Dict, Any
from .database import db

class Client:
    """Modèle pour gérer les clients de l'entreprise"""
    
    def __init__(self, id: Optional[int] = None, nom: str = "", telephone: str = "", 
                 adresse: str = "", solde: float = 0.0):
        """
        Initialise un client
        
        Args:
            id: Identifiant unique du client
            nom: Nom du client
            telephone: Numéro de téléphone
            adresse: Adresse du client
            solde: Solde du compte client
        """
        self.id = id
        self.nom = nom
        self.telephone = telephone
        self.adresse = adresse
        self.solde = solde
    
    def save(self) -> int:
        """
        Sauvegarde le client dans la base de données
        
        Returns:
            ID du client créé ou modifié
        """
        if self.id is None:
            # Nouveau client
            cursor = db.execute_query(
                """INSERT INTO clients (nom, telephone, adresse, solde) 
                   VALUES (?, ?, ?, ?)""",
                (self.nom, self.telephone, self.adresse, self.solde)
            )
            self.id = cursor.lastrowid
            db.commit()
            print(f"✅ Client '{self.nom}' créé avec l'ID {self.id}")
        else:
            # Mise à jour client existant
            db.execute_query(
                """UPDATE clients SET nom=?, telephone=?, adresse=?, solde=? 
                   WHERE id=?""",
                (self.nom, self.telephone, self.adresse, self.solde, self.id)
            )
            db.commit()
            print(f"✅ Client '{self.nom}' mis à jour")
        
        return self.id
    
    def delete(self) -> bool:
        """
        Supprime le client de la base de données
        
        Returns:
            True si suppression réussie, False sinon
        """
        if self.id is None:
            print("❌ Impossible de supprimer un client sans ID")
            return False
        
        cursor = db.execute_query("DELETE FROM clients WHERE id=?", (self.id,))
        db.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ Client '{self.nom}' supprimé")
            return True
        else:
            print(f"❌ Client avec l'ID {self.id} non trouvé")
            return False
    
    def mettre_a_jour_solde(self, montant: float, operation: str = "ajouter"):
        """
        Met à jour le solde du client
        
        Args:
            montant: Montant à ajouter ou soustraire
            operation: "ajouter" ou "soustraire"
        """
        if operation == "ajouter":
            self.solde += montant
        elif operation == "soustraire":
            self.solde -= montant
        
        if self.id:
            db.execute_query(
                "UPDATE clients SET solde=? WHERE id=?",
                (self.solde, self.id)
            )
            db.commit()
    
    @classmethod
    def get_by_id(cls, client_id: int) -> Optional['Client']:
        """
        Récupère un client par son ID
        
        Args:
            client_id: ID du client à récupérer
            
        Returns:
            Instance Client ou None si non trouvé
        """
        cursor = db.execute_query("SELECT * FROM clients WHERE id=?", (client_id,))
        row = cursor.fetchone()
        
        if row:
            return cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            )
        return None
    
    @classmethod
    def get_all(cls) -> List['Client']:
        """
        Récupère tous les clients
        
        Returns:
            Liste de tous les clients
        """
        cursor = db.execute_query("SELECT * FROM clients ORDER BY nom")
        clients = []
        
        for row in cursor.fetchall():
            clients.append(cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            ))
        
        return clients
    
    @classmethod
    def search_by_name(cls, nom: str) -> List['Client']:
        """
        Recherche des clients par nom
        
        Args:
            nom: Nom ou partie du nom à rechercher
            
        Returns:
            Liste des clients correspondants
        """
        cursor = db.execute_query(
            "SELECT * FROM clients WHERE nom LIKE ? ORDER BY nom",
            (f"%{nom}%",)
        )
        clients = []
        
        for row in cursor.fetchall():
            clients.append(cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            ))
        
        return clients
    
    @classmethod
    def get_soldes_positifs(cls) -> List['Client']:
        """
        Récupère les clients avec un solde positif (créditeurs)
        
        Returns:
            Liste des clients avec solde > 0
        """
        cursor = db.execute_query(
            "SELECT * FROM clients WHERE solde > 0 ORDER BY solde DESC"
        )
        clients = []
        
        for row in cursor.fetchall():
            clients.append(cls(
                id=row['id'],
                nom=row['nom'],
                telephone=row['telephone'],
                adresse=row['adresse'],
                solde=row['solde']
            ))
        
        return clients
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit le client en dictionnaire
        
        Returns:
            Dictionnaire représentant le client
        """
        return {
            'id': self.id,
            'nom': self.nom,
            'telephone': self.telephone,
            'adresse': self.adresse,
            'solde': self.solde
        }
    
    def __str__(self) -> str:
        """Représentation string du client"""
        return f"Client(ID: {self.id}, Nom: {self.nom}, Solde: {self.solde:.2f} DH)"
    
    def __repr__(self) -> str:
        """Représentation détaillée du client"""
        return (f"Client(id={self.id}, nom='{self.nom}', telephone='{self.telephone}', "
                f"adresse='{self.adresse}', solde={self.solde})")
