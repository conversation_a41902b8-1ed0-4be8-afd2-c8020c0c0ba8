"""
Génération de rapports pour le système de comptabilité COMTABELITE
Rapports mensuels, annuels et analyses financières
"""

from typing import Dict, List, Any
from datetime import datetime, timedelta
from models.client import Client
from models.fournisseur import Fournisseur
from models.produit import Produit
from models.transaction import Transaction
from models.employe import Employe
from models.declaration import DeclarationFiscale, DeclarationSociale
from utils.calculs import calculer_tva_periode, calculer_chiffre_affaires_periode, calculer_is_annuel

def generer_rapport_mensuel(mois: str) -> Dict[str, Any]:
    """
    Génère un rapport mensuel complet
    
    Args:
        mois: Mois au format YYYY-MM
        
    Returns:
        Dictionnaire contenant tous les éléments du rapport mensuel
    """
    # Calculer les dates de début et fin du mois
    annee, mois_num = map(int, mois.split('-'))
    debut = datetime(annee, mois_num, 1)
    
    # Dernier jour du mois
    if mois_num == 12:
        fin = datetime(annee + 1, 1, 1) - timedelta(days=1)
    else:
        fin = datetime(annee, mois_num + 1, 1) - timedelta(days=1)
    fin = fin.replace(hour=23, minute=59, second=59)
    
    # Chiffre d'affaires et TVA
    ca_data = calculer_chiffre_affaires_periode(debut, fin)
    tva_data = calculer_tva_periode(debut, fin)
    
    # Soldes clients
    clients = Client.get_all()
    soldes_clients = [
        {
            'nom': client.nom,
            'solde': client.solde,
            'telephone': client.telephone
        }
        for client in clients if client.solde != 0
    ]
    
    # Soldes fournisseurs
    fournisseurs = Fournisseur.get_all()
    soldes_fournisseurs = [
        {
            'nom': fournisseur.nom,
            'solde': fournisseur.solde,
            'telephone': fournisseur.telephone
        }
        for fournisseur in fournisseurs if fournisseur.solde != 0
    ]
    
    # Masse salariale
    masse_salariale = Employe.calculer_masse_salariale()
    
    # Déclarations sociales du mois
    declarations_sociales = DeclarationSociale.get_by_mois(mois)
    totaux_sociaux = DeclarationSociale.calculer_totaux_mois(mois)
    
    # Stock des produits
    produits = Produit.get_all()
    etat_stock = [
        {
            'nom': produit.nom,
            'quantite': produit.quantite,
            'unite': produit.unite,
            'valeur_stock': produit.calculer_valeur_stock(),
            'prix_vente': produit.prix_vente
        }
        for produit in produits
    ]
    
    # Transactions du mois
    transactions = Transaction.get_by_period(debut, fin)
    
    rapport = {
        'periode': mois,
        'date_generation': datetime.now().isoformat(),
        
        # Chiffre d'affaires
        'chiffre_affaires': ca_data,
        
        # TVA
        'tva': tva_data,
        
        # Soldes
        'soldes_clients': soldes_clients,
        'total_creances': sum(client['solde'] for client in soldes_clients if client['solde'] > 0),
        'soldes_fournisseurs': soldes_fournisseurs,
        'total_dettes': sum(abs(fournisseur['solde']) for fournisseur in soldes_fournisseurs if fournisseur['solde'] < 0),
        
        # Salaires
        'masse_salariale': masse_salariale,
        'declarations_sociales': totaux_sociaux,
        
        # Stock
        'etat_stock': etat_stock,
        'valeur_totale_stock': sum(item['valeur_stock'] for item in etat_stock),
        
        # Transactions
        'nombre_transactions': len(transactions),
        'nombre_ventes': len([t for t in transactions if t.type == 'vente']),
        'nombre_achats': len([t for t in transactions if t.type == 'achat'])
    }
    
    return rapport

def generer_bilan_annuel(annee: int) -> Dict[str, Any]:
    """
    Génère un bilan annuel complet
    
    Args:
        annee: Année de référence
        
    Returns:
        Dictionnaire contenant le bilan annuel
    """
    debut = datetime(annee, 1, 1)
    fin = datetime(annee, 12, 31, 23, 59, 59)
    
    # Chiffre d'affaires annuel
    ca_annuel = calculer_chiffre_affaires_periode(debut, fin)
    
    # Calcul IS
    is_data = calculer_is_annuel(annee)
    
    # TVA annuelle
    tva_annuelle = calculer_tva_periode(debut, fin)
    
    # Évolution mensuelle
    evolution_mensuelle = []
    for mois in range(1, 13):
        mois_str = f"{annee}-{mois:02d}"
        debut_mois = datetime(annee, mois, 1)
        
        if mois == 12:
            fin_mois = datetime(annee + 1, 1, 1) - timedelta(days=1)
        else:
            fin_mois = datetime(annee, mois + 1, 1) - timedelta(days=1)
        fin_mois = fin_mois.replace(hour=23, minute=59, second=59)
        
        ca_mois = calculer_chiffre_affaires_periode(debut_mois, fin_mois)
        evolution_mensuelle.append({
            'mois': mois_str,
            'ca_ht': ca_mois['chiffre_affaires_ht'],
            'achats_ht': ca_mois['achats_ht'],
            'benefice': ca_mois['benefice_brut']
        })
    
    # Déclarations fiscales de l'année
    declarations_fiscales = []
    for type_impot in ['TVA', 'IS', 'IR']:
        declarations = DeclarationFiscale.get_by_type(type_impot)
        declarations_annee = [d for d in declarations if d.periode.startswith(str(annee))]
        total_type = sum(d.montant for d in declarations_annee)
        declarations_fiscales.append({
            'type': type_impot,
            'nombre_declarations': len(declarations_annee),
            'total_paye': total_type
        })
    
    # Analyse des produits
    produits = Produit.get_all()
    analyse_produits = []
    for produit in produits:
        transactions_produit = [t for t in Transaction.get_all() 
                              if t.produit_id == produit.id and 
                              debut <= t.date_transaction <= fin]
        
        ventes = [t for t in transactions_produit if t.type == 'vente']
        achats = [t for t in transactions_produit if t.type == 'achat']
        
        analyse_produits.append({
            'nom': produit.nom,
            'quantite_vendue': sum(v.quantite for v in ventes),
            'ca_produit': sum(v.total for v in ventes),
            'quantite_achetee': sum(a.quantite for a in achats),
            'cout_achats': sum(a.total for a in achats),
            'stock_final': produit.quantite,
            'marge_moyenne': produit.calculer_marge()
        })
    
    bilan = {
        'annee': annee,
        'date_generation': datetime.now().isoformat(),
        
        # Résultats financiers
        'chiffre_affaires_annuel': ca_annuel,
        'impot_societes': is_data,
        'tva_annuelle': tva_annuelle,
        
        # Évolution
        'evolution_mensuelle': evolution_mensuelle,
        'croissance_ca': _calculer_croissance(evolution_mensuelle),
        
        # Fiscalité
        'declarations_fiscales': declarations_fiscales,
        'total_impots_payes': sum(d['total_paye'] for d in declarations_fiscales),
        
        # Analyse produits
        'analyse_produits': analyse_produits,
        'produit_plus_rentable': max(analyse_produits, key=lambda x: x['marge_moyenne'], default=None),
        'produit_plus_vendu': max(analyse_produits, key=lambda x: x['quantite_vendue'], default=None)
    }
    
    return bilan

def _calculer_croissance(evolution_mensuelle: List[Dict]) -> Dict[str, float]:
    """
    Calcule la croissance du chiffre d'affaires
    
    Args:
        evolution_mensuelle: Liste des données mensuelles
        
    Returns:
        Dictionnaire avec les indicateurs de croissance
    """
    if len(evolution_mensuelle) < 2:
        return {'croissance_annuelle': 0.0, 'tendance': 'stable'}
    
    premier_mois = evolution_mensuelle[0]['ca_ht']
    dernier_mois = evolution_mensuelle[-1]['ca_ht']
    
    if premier_mois > 0:
        croissance = ((dernier_mois - premier_mois) / premier_mois) * 100
    else:
        croissance = 0.0
    
    # Déterminer la tendance
    if croissance > 5:
        tendance = 'croissance'
    elif croissance < -5:
        tendance = 'décroissance'
    else:
        tendance = 'stable'
    
    return {
        'croissance_annuelle': croissance,
        'tendance': tendance,
        'ca_premier_mois': premier_mois,
        'ca_dernier_mois': dernier_mois
    }

def afficher_rapport_mensuel(mois: str):
    """
    Affiche le rapport mensuel dans le terminal
    
    Args:
        mois: Mois au format YYYY-MM
    """
    rapport = generer_rapport_mensuel(mois)
    
    print(f"\n{'='*60}")
    print(f"           RAPPORT MENSUEL - {mois}")
    print(f"{'='*60}")
    print(f"Généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M')}")
    
    # Chiffre d'affaires
    ca = rapport['chiffre_affaires']
    print(f"\n📊 CHIFFRE D'AFFAIRES:")
    print(f"   • CA HT: {ca['chiffre_affaires_ht']:,.2f} DH")
    print(f"   • CA TTC: {ca['chiffre_affaires_ttc']:,.2f} DH")
    print(f"   • Achats HT: {ca['achats_ht']:,.2f} DH")
    print(f"   • Bénéfice brut: {ca['benefice_brut']:,.2f} DH")
    
    # TVA
    tva = rapport['tva']
    print(f"\n💰 TVA:")
    print(f"   • TVA collectée: {tva['tva_collectee']:,.2f} DH")
    print(f"   • TVA déductible: {tva['tva_deductible']:,.2f} DH")
    print(f"   • TVA à payer: {tva['tva_a_payer']:,.2f} DH")
    
    # Soldes
    print(f"\n👥 SOLDES:")
    print(f"   • Total créances clients: {rapport['total_creances']:,.2f} DH")
    print(f"   • Total dettes fournisseurs: {rapport['total_dettes']:,.2f} DH")
    
    # Salaires
    masse = rapport['masse_salariale']
    print(f"\n💼 MASSE SALARIALE:")
    print(f"   • Nombre d'employés: {masse['nombre_employes']}")
    print(f"   • Total brut: {masse['total_brut']:,.2f} DH")
    print(f"   • Total net: {masse['total_net']:,.2f} DH")
    print(f"   • Total charges: {masse['total_charges']:,.2f} DH")
    
    # Stock
    print(f"\n📦 STOCK:")
    print(f"   • Valeur totale: {rapport['valeur_totale_stock']:,.2f} DH")
    print(f"   • Nombre de produits: {len(rapport['etat_stock'])}")
    
    # Transactions
    print(f"\n📋 ACTIVITÉ:")
    print(f"   • Total transactions: {rapport['nombre_transactions']}")
    print(f"   • Ventes: {rapport['nombre_ventes']}")
    print(f"   • Achats: {rapport['nombre_achats']}")
    
    print(f"\n{'='*60}")

def afficher_bilan_annuel(annee: int):
    """
    Affiche le bilan annuel dans le terminal
    
    Args:
        annee: Année de référence
    """
    bilan = generer_bilan_annuel(annee)
    
    print(f"\n{'='*70}")
    print(f"                BILAN ANNUEL - {annee}")
    print(f"{'='*70}")
    print(f"Généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M')}")
    
    # Résultats financiers
    ca = bilan['chiffre_affaires_annuel']
    print(f"\n📈 RÉSULTATS FINANCIERS:")
    print(f"   • CA annuel HT: {ca['chiffre_affaires_ht']:,.2f} DH")
    print(f"   • Achats annuels HT: {ca['achats_ht']:,.2f} DH")
    print(f"   • Bénéfice brut: {ca['benefice_brut']:,.2f} DH")
    
    # Impôts
    is_data = bilan['impot_societes']
    print(f"\n🏛️ FISCALITÉ:")
    print(f"   • IS à payer: {is_data['is_a_payer']:,.2f} DH")
    print(f"   • Total impôts payés: {bilan['total_impots_payes']:,.2f} DH")
    
    # Croissance
    croissance = bilan['croissance_ca']
    print(f"\n📊 CROISSANCE:")
    print(f"   • Croissance annuelle: {croissance['croissance_annuelle']:+.1f}%")
    print(f"   • Tendance: {croissance['tendance']}")
    
    # Produits
    if bilan['produit_plus_rentable']:
        produit_rentable = bilan['produit_plus_rentable']
        print(f"\n🏆 PRODUIT LE PLUS RENTABLE:")
        print(f"   • {produit_rentable['nom']}: {produit_rentable['marge_moyenne']:.1f}% de marge")
    
    if bilan['produit_plus_vendu']:
        produit_vendu = bilan['produit_plus_vendu']
        print(f"\n🔥 PRODUIT LE PLUS VENDU:")
        print(f"   • {produit_vendu['nom']}: {produit_vendu['quantite_vendue']:,.0f} unités")
    
    print(f"\n{'='*70}")
