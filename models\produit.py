"""
Modèle Produit pour le système de comptabilité COMTABELITE
Gestion des carburants et produits pétroliers
"""

from typing import List, Optional, Dict, Any
from .database import db

class Produit:
    """Modèle pour gérer les produits (carburants) de l'entreprise"""

    def __init__(self, id: Optional[int] = None, nom: str = "", prix_achat: float = 0.0,
                 prix_vente: float = 0.0, quantite: float = 0.0, unite: str = "L"):
        """
        Initialise un produit

        Args:
            id: Identifiant unique du produit
            nom: Nom du produit (ex: Gasoil, Essence Super, etc.)
            prix_achat: Prix d'achat unitaire
            prix_vente: Prix de vente unitaire
            quantite: Quantité en stock
            unite: Unité de mesure (L pour litres par défaut)
        """
        self.id = id
        self.nom = nom
        self.prix_achat = prix_achat
        self.prix_vente = prix_vente
        self.quantite = quantite
        self.unite = unite

    def save(self) -> int:
        """
        Sauvegarde le produit dans la base de données

        Returns:
            ID du produit créé ou modifié
        """
        if self.id is None:
            # Nouveau produit
            cursor = db.execute_query(
                """INSERT INTO produits (nom, prix_achat, prix_vente, quantite, unite)
                   VALUES (?, ?, ?, ?, ?)""",
                (self.nom, self.prix_achat, self.prix_vente, self.quantite, self.unite)
            )
            self.id = cursor.lastrowid
            db.commit()
            print(f"✅ Produit '{self.nom}' créé avec l'ID {self.id}")
        else:
            # Mise à jour produit existant
            db.execute_query(
                """UPDATE produits SET nom=?, prix_achat=?, prix_vente=?, quantite=?, unite=?
                   WHERE id=?""",
                (self.nom, self.prix_achat, self.prix_vente, self.quantite, self.unite, self.id)
            )
            db.commit()
            print(f"✅ Produit '{self.nom}' mis à jour")

        return self.id

    def delete(self) -> bool:
        """
        Supprime le produit de la base de données

        Returns:
            True si suppression réussie, False sinon
        """
        if self.id is None:
            print("❌ Impossible de supprimer un produit sans ID")
            return False

        cursor = db.execute_query("DELETE FROM produits WHERE id=?", (self.id,))
        db.commit()

        if cursor.rowcount > 0:
            print(f"✅ Produit '{self.nom}' supprimé")
            return True
        else:
            print(f"❌ Produit avec l'ID {self.id} non trouvé")
            return False

    def mettre_a_jour_stock(self, quantite: float, operation: str = "ajouter"):
        """
        Met à jour le stock du produit

        Args:
            quantite: Quantité à ajouter ou soustraire
            operation: "ajouter" ou "soustraire"
        """
        if operation == "ajouter":
            self.quantite += quantite
        elif operation == "soustraire":
            self.quantite -= quantite
            if self.quantite < 0:
                print(f"⚠️ Attention: Stock négatif pour {self.nom}: {self.quantite}")

        if self.id:
            db.execute_query(
                "UPDATE produits SET quantite=? WHERE id=?",
                (self.quantite, self.id)
            )
            db.commit()

    def calculer_marge(self) -> float:
        """
        Calcule la marge bénéficiaire du produit

        Returns:
            Marge en pourcentage
        """
        if self.prix_achat > 0:
            return ((self.prix_vente - self.prix_achat) / self.prix_achat) * 100
        return 0.0

    def calculer_valeur_stock(self) -> float:
        """
        Calcule la valeur du stock au prix d'achat

        Returns:
            Valeur totale du stock
        """
        return self.quantite * self.prix_achat

    @classmethod
    def get_by_id(cls, produit_id: int) -> Optional['Produit']:
        """
        Récupère un produit par son ID

        Args:
            produit_id: ID du produit à récupérer

        Returns:
            Instance Produit ou None si non trouvé
        """
        cursor = db.execute_query("SELECT * FROM produits WHERE id=?", (produit_id,))
        row = cursor.fetchone()

        if row:
            return cls(
                id=row['id'],
                nom=row['nom'],
                prix_achat=row['prix_achat'],
                prix_vente=row['prix_vente'],
                quantite=row['quantite'],
                unite=row['unite']
            )
        return None

    @classmethod
    def get_all(cls) -> List['Produit']:
        """
        Récupère tous les produits

        Returns:
            Liste de tous les produits
        """
        cursor = db.execute_query("SELECT * FROM produits ORDER BY nom")
        produits = []

        for row in cursor.fetchall():
            produits.append(cls(
                id=row['id'],
                nom=row['nom'],
                prix_achat=row['prix_achat'],
                prix_vente=row['prix_vente'],
                quantite=row['quantite'],
                unite=row['unite']
            ))

        return produits

    @classmethod
    def get_stock_faible(cls, seuil: float = 100.0) -> List['Produit']:
        """
        Récupère les produits avec un stock faible

        Args:
            seuil: Seuil en dessous duquel le stock est considéré comme faible

        Returns:
            Liste des produits avec stock faible
        """
        cursor = db.execute_query(
            "SELECT * FROM produits WHERE quantite < ? ORDER BY quantite ASC",
            (seuil,)
        )
        produits = []

        for row in cursor.fetchall():
            produits.append(cls(
                id=row['id'],
                nom=row['nom'],
                prix_achat=row['prix_achat'],
                prix_vente=row['prix_vente'],
                quantite=row['quantite'],
                unite=row['unite']
            ))

        return produits

    @classmethod
    def search_by_name(cls, nom: str) -> List['Produit']:
        """
        Recherche des produits par nom

        Args:
            nom: Nom ou partie du nom à rechercher

        Returns:
            Liste des produits correspondants
        """
        cursor = db.execute_query(
            "SELECT * FROM produits WHERE nom LIKE ? ORDER BY nom",
            (f"%{nom}%",)
        )
        produits = []

        for row in cursor.fetchall():
            produits.append(cls(
                id=row['id'],
                nom=row['nom'],
                prix_achat=row['prix_achat'],
                prix_vente=row['prix_vente'],
                quantite=row['quantite'],
                unite=row['unite']
            ))

        return produits

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit le produit en dictionnaire

        Returns:
            Dictionnaire représentant le produit
        """
        return {
            'id': self.id,
            'nom': self.nom,
            'prix_achat': self.prix_achat,
            'prix_vente': self.prix_vente,
            'quantite': self.quantite,
            'unite': self.unite,
            'marge': self.calculer_marge(),
            'valeur_stock': self.calculer_valeur_stock()
        }

    def __str__(self) -> str:
        """Représentation string du produit"""
        return f"Produit(ID: {self.id}, Nom: {self.nom}, Stock: {self.quantite} {self.unite})"

    def __repr__(self) -> str:
        """Représentation détaillée du produit"""
        return (f"Produit(id={self.id}, nom='{self.nom}', prix_achat={self.prix_achat}, "
                f"prix_vente={self.prix_vente}, quantite={self.quantite}, unite='{self.unite}')")
