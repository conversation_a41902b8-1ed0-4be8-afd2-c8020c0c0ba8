"""
Configuration du système COMTABELITE
Paramètres fiscaux et sociaux selon la législation marocaine
"""

# ===== TAUX DE COTISATIONS SOCIALES (2024) =====

# Cotisations employé
TAUX_CNSS_EMPLOYE = 0.0448      # 4.48%
TAUX_AMO_EMPLOYE = 0.0226       # 2.26%

# Cotisations patronales
TAUX_CNSS_PATRON = 0.2093       # 20.93%
TAUX_AMO_PATRON = 0.0226        # 2.26%
TAUX_FORMATION = 0.016          # 1.6% (Formation professionnelle)

# Plafonds de cotisations
PLAFOND_CNSS = 6000.0           # 6000 DH/mois
PLAFOND_AMO = 5000.0            # 5000 DH/mois

# ===== BARÈME IMPÔT SUR LE REVENU (2024) =====

# Tranches mensuelles IR (en DH)
BAREME_IR = [
    {"min": 0,       "max": 2500,    "taux": 0.00},    # Exonéré
    {"min": 2500,    "max": 4166.67, "taux": 0.10},    # 10%
    {"min": 4166.67, "max": 5000,    "taux": 0.20},    # 20%
    {"min": 5000,    "max": 6666.67, "taux": 0.30},    # 30%
    {"min": 6666.67, "max": 15000,   "taux": 0.34},    # 34%
    {"min": 15000,   "max": float('inf'), "taux": 0.38} # 38%
]

# ===== TAUX FISCAUX =====

# TVA
TAUX_TVA_STANDARD = 0.20        # 20%
TAUX_TVA_REDUIT = 0.10          # 10% (certains produits)
TAUX_TVA_SUPER_REDUIT = 0.07    # 7% (produits de première nécessité)

# Impôt sur les Sociétés
TAUX_IS_STANDARD = 0.31         # 31%
TAUX_IS_PME = 0.20              # 20% (pour CA < 3M DH)

# ===== PARAMÈTRES ENTREPRISE =====

# Informations par défaut
ENTREPRISE_INFO = {
    "nom": "Entreprise de Carburants",
    "secteur": "Distribution de carburants",
    "regime_fiscal": "Régime général",
    "regime_social": "CNSS"
}

# Seuils d'alerte
SEUIL_STOCK_FAIBLE = 100.0      # Litres
SEUIL_PME_IS = 3000000.0        # 3M DH de CA pour taux IS réduit

# ===== PARAMÈTRES TECHNIQUES =====

# Base de données
DB_PATH = "data/fuel_company.db"
BACKUP_PATH = "data/backups/"

# Formats
FORMAT_DATE = "%d/%m/%Y"
FORMAT_DATETIME = "%d/%m/%Y à %H:%M"
FORMAT_MOIS = "%Y-%m"

# Devise
DEVISE = "DH"

# ===== MESSAGES =====

MESSAGES = {
    "bienvenue": "🚀 Bienvenue dans COMTABELITE!",
    "au_revoir": "👋 Au revoir!",
    "erreur_generale": "❌ Une erreur s'est produite",
    "succes_creation": "✅ Créé avec succès",
    "succes_modification": "✅ Modifié avec succès",
    "succes_suppression": "✅ Supprimé avec succès",
    "confirmation_suppression": "Êtes-vous sûr de vouloir supprimer ?",
    "stock_insuffisant": "❌ Stock insuffisant",
    "format_invalide": "❌ Format invalide"
}

# ===== VALIDATION =====

# Formats de validation
REGEX_CIN = r'^[A-Za-z]{1,2}\d{6,8}$'
REGEX_TELEPHONE = [
    r'^0[567]\d{8}$',           # Format national
    r'^\+212[567]\d{8}$',       # Format international avec +
    r'^00212[567]\d{8}$'        # Format international avec 00
]

# Limites
MONTANT_MAX = *********.99      # Montant maximum
QUANTITE_MAX = 999999.99        # Quantité maximum
SALAIRE_MIN = 2000.0            # SMIG approximatif
SALAIRE_MAX = 100000.0          # Salaire maximum raisonnable

# ===== FONCTIONS UTILITAIRES =====

def get_taux_is(chiffre_affaires: float) -> float:
    """
    Retourne le taux d'IS applicable selon le CA
    
    Args:
        chiffre_affaires: Chiffre d'affaires annuel
        
    Returns:
        Taux d'IS applicable
    """
    if chiffre_affaires < SEUIL_PME_IS:
        return TAUX_IS_PME
    return TAUX_IS_STANDARD

def get_taux_tva(type_produit: str = "standard") -> float:
    """
    Retourne le taux de TVA applicable
    
    Args:
        type_produit: Type de produit (standard, reduit, super_reduit)
        
    Returns:
        Taux de TVA applicable
    """
    taux_map = {
        "standard": TAUX_TVA_STANDARD,
        "reduit": TAUX_TVA_REDUIT,
        "super_reduit": TAUX_TVA_SUPER_REDUIT
    }
    return taux_map.get(type_produit, TAUX_TVA_STANDARD)

def valider_salaire(salaire: float) -> bool:
    """
    Valide qu'un salaire est dans les limites acceptables
    
    Args:
        salaire: Salaire à valider
        
    Returns:
        True si valide, False sinon
    """
    return SALAIRE_MIN <= salaire <= SALAIRE_MAX

def formater_devise(montant: float) -> str:
    """
    Formate un montant avec la devise
    
    Args:
        montant: Montant à formater
        
    Returns:
        Montant formaté avec devise
    """
    return f"{montant:,.2f} {DEVISE}"

# ===== EXPORT DE CONFIGURATION =====

# Configuration complète pour export
CONFIG = {
    "cotisations": {
        "cnss_employe": TAUX_CNSS_EMPLOYE,
        "amo_employe": TAUX_AMO_EMPLOYE,
        "cnss_patron": TAUX_CNSS_PATRON,
        "amo_patron": TAUX_AMO_PATRON,
        "formation": TAUX_FORMATION,
        "plafond_cnss": PLAFOND_CNSS,
        "plafond_amo": PLAFOND_AMO
    },
    "fiscalite": {
        "tva_standard": TAUX_TVA_STANDARD,
        "tva_reduit": TAUX_TVA_REDUIT,
        "is_standard": TAUX_IS_STANDARD,
        "is_pme": TAUX_IS_PME,
        "bareme_ir": BAREME_IR
    },
    "entreprise": ENTREPRISE_INFO,
    "seuils": {
        "stock_faible": SEUIL_STOCK_FAIBLE,
        "pme_is": SEUIL_PME_IS,
        "salaire_min": SALAIRE_MIN,
        "salaire_max": SALAIRE_MAX
    },
    "technique": {
        "db_path": DB_PATH,
        "backup_path": BACKUP_PATH,
        "formats": {
            "date": FORMAT_DATE,
            "datetime": FORMAT_DATETIME,
            "mois": FORMAT_MOIS
        },
        "devise": DEVISE
    }
}

if __name__ == "__main__":
    print("Configuration COMTABELITE")
    print("=" * 30)
    print(f"Taux CNSS employé: {TAUX_CNSS_EMPLOYE*100:.2f}%")
    print(f"Taux AMO employé: {TAUX_AMO_EMPLOYE*100:.2f}%")
    print(f"Taux TVA standard: {TAUX_TVA_STANDARD*100:.0f}%")
    print(f"Taux IS standard: {TAUX_IS_STANDARD*100:.0f}%")
    print(f"Plafond CNSS: {formater_devise(PLAFOND_CNSS)}")
    print(f"Plafond AMO: {formater_devise(PLAFOND_AMO)}")
