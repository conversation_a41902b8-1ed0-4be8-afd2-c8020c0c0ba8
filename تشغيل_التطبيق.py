#!/usr/bin/env python3
"""
تشغيل تطبيق COMTABELITE - نظام المحاسبة للمحروقات
"""

import sys
import os

# إضافة المجلد الجذر للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def عرض_الشعار():
    """عرض شعار التطبيق"""
    print("""
    ╔═══════════════════════════════════════════════════════════╗
    ║                                                           ║
    ║    ██████╗ ██████╗ ███╗   ███╗████████╗ █████╗ ██████╗    ║
    ║   ██╔════╝██╔═══██╗████╗ ████║╚══██╔══╝██╔══██╗██╔══██╗   ║
    ║   ██║     ██║   ██║██╔████╔██║   ██║   ███████║██████╔╝   ║
    ║   ██║     ██║   ██║██║╚██╔╝██║   ██║   ██╔══██║██╔══██╗   ║
    ║   ╚██████╗╚██████╔╝██║ ╚═╝ ██║   ██║   ██║  ██║██████╔╝   ║
    ║    ╚═════╝ ╚═════╝ ╚═╝     ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═════╝    ║
    ║                                                           ║
    ║              نظام المحاسبة للمحروقات - المغرب              ║
    ║                     الإصدار 1.0                          ║
    ║                                                           ║
    ╚═══════════════════════════════════════════════════════════╝
    """)

def عرض_القائمة_الرئيسية():
    """عرض القائمة الرئيسية"""
    print("\n🚀 مرحباً بك في COMTABELITE!")
    print("نظام المحاسبة للشركات المغربية المتخصصة في المحروقات")
    print("\n" + "="*50)
    print("           القائمة الرئيسية")
    print("="*50)
    print("1. إدارة العملاء")
    print("2. إدارة الموردين") 
    print("3. إدارة المنتجات")
    print("4. إدارة الموظفين")
    print("5. المعاملات")
    print("6. الإقرارات الضريبية")
    print("7. التقارير والتحليلات")
    print("8. الأدوات المساعدة")
    print("9. الخروج")
    print("="*50)

def عرض_الإحصائيات():
    """عرض إحصائيات النظام"""
    try:
        from models.client import Client
        from models.fournisseur import Fournisseur
        from models.produit import Produit
        from models.employe import Employe
        from models.transaction import Transaction
        
        print("\n📊 إحصائيات النظام:")
        print("="*30)
        
        عدد_العملاء = len(Client.get_all())
        عدد_الموردين = len(Fournisseur.get_all())
        عدد_المنتجات = len(Produit.get_all())
        عدد_الموظفين = len(Employe.get_all())
        عدد_المعاملات = len(Transaction.get_all())
        
        print(f"• العملاء: {عدد_العملاء}")
        print(f"• الموردين: {عدد_الموردين}")
        print(f"• المنتجات: {عدد_المنتجات}")
        print(f"• الموظفين: {عدد_الموظفين}")
        print(f"• المعاملات: {عدد_المعاملات}")
        
        if عدد_المنتجات > 0:
            منتجات = Produit.get_all()
            قيمة_المخزون = sum(p.calculer_valeur_stock() for p in منتجات)
            print(f"• قيمة المخزون الإجمالية: {قيمة_المخزون:,.2f} درهم")
        
        if عدد_الموظفين > 0:
            كتلة_الأجور = Employe.calculer_masse_salariale()
            print(f"• كتلة الأجور الشهرية: {كتلة_الأجور['total_brut']:,.2f} درهم")
            
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {e}")

def عرض_مثال_حساب_الراتب():
    """عرض مثال على حساب الراتب"""
    print("\n💰 مثال على حساب الراتب (8000 درهم):")
    print("="*40)
    
    try:
        from utils.calculs import calculer_salaire
        
        نتيجة = calculer_salaire(8000.0)
        
        print(f"• الراتب الإجمالي: {نتيجة['salaire_brut']:,.2f} درهم")
        print(f"• الضمان الاجتماعي (4.48%): {نتيجة['cnss']:,.2f} درهم")
        print(f"• التأمين الإجباري (2.26%): {نتيجة['amo']:,.2f} درهم")
        print(f"• الضريبة على الدخل: {نتيجة['ir']:,.2f} درهم")
        print(f"• إجمالي الاستقطاعات: {نتيجة['total_deductions']:,.2f} درهم")
        print(f"• الراتب الصافي: {نتيجة['salaire_net']:,.2f} درهم")
        
    except Exception as e:
        print(f"❌ خطأ في حساب الراتب: {e}")

def عرض_مثال_معاملة():
    """عرض مثال على معاملة بيع"""
    print("\n🛒 مثال على معاملة بيع:")
    print("="*30)
    print("بيع 1000 لتر من الغازوال بسعر 14.20 درهم/لتر")
    print("• المبلغ بدون ضريبة: 14,200.00 درهم")
    print("• ضريبة القيمة المضافة (20%): 2,840.00 درهم")
    print("• المبلغ الإجمالي: 17,040.00 درهم")
    print("• تحديث المخزون تلقائياً: 5000 لتر ← 4000 لتر")
    print("• إضافة المبلغ لرصيد العميل تلقائياً")

def تشغيل_العرض_التوضيحي():
    """تشغيل العرض التوضيحي للتطبيق"""
    
    print("🚀 تشغيل نظام COMTABELITE")
    print("="*50)
    
    # التحقق من المكونات
    print("📦 التحقق من مكونات النظام...")
    
    try:
        from models.database import db
        print("   ✅ قاعدة البيانات")
        
        # إنشاء الجداول
        db.create_tables()
        print("   ✅ إنشاء الجداول")
        
        # عرض الشعار
        عرض_الشعار()
        
        # عرض القائمة الرئيسية
        عرض_القائمة_الرئيسية()
        
        # عرض الإحصائيات
        عرض_الإحصائيات()
        
        # عرض أمثلة
        عرض_مثال_حساب_الراتب()
        عرض_مثال_معاملة()
        
        print("\n" + "="*50)
        print("🎉 النظام جاهز للاستخدام!")
        print("="*50)
        
        print("\n🚀 لتشغيل التطبيق الكامل:")
        print("   python main.py")
        print("\n📚 للعرض التوضيحي:")
        print("   python demo.py")
        print("\n🧪 للاختبارات:")
        print("   python test_simple.py")
        print("\n🔧 للتحقق من التثبيت:")
        print("   python verifier_installation.py")
        
        # إغلاق قاعدة البيانات
        db.close()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("\n🔧 الحلول المقترحة:")
        print("   • تأكد من تثبيت Python 3.11+")
        print("   • تأكد من وجود جميع الملفات")
        print("   • شغل: python verifier_installation.py")

if __name__ == "__main__":
    تشغيل_العرض_التوضيحي()
