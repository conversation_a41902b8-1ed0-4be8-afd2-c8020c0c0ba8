#!/usr/bin/env python3
"""
Démonstration des fonctionnalités de COMTABELITE
Script de test automatisé pour valider toutes les fonctionnalités
"""

import sys
import os
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import db
from models.client import Client
from models.fournisseur import Fournisseur
from models.produit import Produit
from models.transaction import Transaction
from models.employe import Employe
from models.declaration import DeclarationFiscale, DeclarationSociale

from utils.calculs import calculer_salaire, declarer_impot, calculer_tva_periode, calculer_is_annuel
from utils.rapports import generer_rapport_mensuel, generer_bilan_annuel
from utils.helpers import formater_montant

def demo_complete():
    """Démonstration complète du système COMTABELITE"""
    
    print("🚀 DÉMONSTRATION COMTABELITE")
    print("=" * 50)
    
    # Initialiser la base de données
    print("\n1️⃣ Initialisation de la base de données...")
    db.create_tables()
    print("✅ Base de données créée")
    
    # Créer des clients
    print("\n2️⃣ Création des clients...")
    clients_data = [
        {"nom": "Station TOTAL Casablanca", "telephone": "0522123456", "adresse": "Bd Mohammed V, Casablanca"},
        {"nom": "Station AFRIQUIA Rabat", "telephone": "0537654321", "adresse": "Av Hassan II, Rabat"},
        {"nom": "Station SHELL Marrakech", "telephone": "0524987654", "adresse": "Route de Fès, Marrakech"}
    ]
    
    clients = []
    for client_data in clients_data:
        client = Client(**client_data)
        client.save()
        clients.append(client)
    
    print(f"✅ {len(clients)} clients créés")
    
    # Créer des fournisseurs
    print("\n3️⃣ Création des fournisseurs...")
    fournisseurs_data = [
        {"nom": "SAMIR Raffinerie", "telephone": "0523111222", "adresse": "Mohammedia"},
        {"nom": "Distributeur Carburants Maroc", "telephone": "0522333444", "adresse": "Casablanca"}
    ]
    
    fournisseurs = []
    for fournisseur_data in fournisseurs_data:
        fournisseur = Fournisseur(**fournisseur_data)
        fournisseur.save()
        fournisseurs.append(fournisseur)
    
    print(f"✅ {len(fournisseurs)} fournisseurs créés")
    
    # Créer des produits
    print("\n4️⃣ Création des produits...")
    produits_data = [
        {"nom": "Gasoil", "prix_achat": 12.50, "prix_vente": 14.20, "quantite": 5000},
        {"nom": "Essence Super", "prix_achat": 13.80, "prix_vente": 15.60, "quantite": 3000},
        {"nom": "Essence Sans Plomb", "prix_achat": 13.20, "prix_vente": 15.00, "quantite": 2500}
    ]
    
    produits = []
    for produit_data in produits_data:
        produit = Produit(**produit_data)
        produit.save()
        produits.append(produit)
        print(f"   • {produit.nom}: Marge {produit.calculer_marge():.1f}%")
    
    print(f"✅ {len(produits)} produits créés")
    
    # Créer des employés
    print("\n5️⃣ Création des employés...")
    employes_data = [
        {"nom": "Ahmed BENALI", "cin": "AB123456", "numero_cnss": "1234567890", "salaire_brut": 8000},
        {"nom": "Fatima ALAOUI", "cin": "FA654321", "numero_cnss": "0987654321", "salaire_brut": 6500},
        {"nom": "Mohamed TAZI", "cin": "MT789012", "numero_cnss": "1122334455", "salaire_brut": 5500}
    ]
    
    employes = []
    for employe_data in employes_data:
        employe = Employe(**employe_data)
        employe.save()
        employes.append(employe)
        print(f"   • {employe.nom}: {formater_montant(employe.salaire_brut)} → {formater_montant(employe.salaire_net)}")
    
    print(f"✅ {len(employes)} employés créés")
    
    # Test des calculs de salaire
    print("\n6️⃣ Test des calculs de salaire...")
    for employe in employes:
        calcul = employe.calculer_salaire()
        print(f"   • {employe.nom}:")
        print(f"     - Brut: {formater_montant(calcul['salaire_brut'])}")
        print(f"     - CNSS: {formater_montant(calcul['cnss'])}")
        print(f"     - AMO: {formater_montant(calcul['amo'])}")
        print(f"     - IR: {formater_montant(calcul['ir'])}")
        print(f"     - Net: {formater_montant(calcul['salaire_net'])}")
    
    # Créer des transactions
    print("\n7️⃣ Création de transactions...")
    
    # Vente 1: 1000L de Gasoil à Station TOTAL
    transaction1 = Transaction.enregistrer_transaction(
        type="vente",
        type_entite="client",
        entite_id=clients[0].id,  # Station TOTAL
        produit_id=produits[0].id,  # Gasoil
        quantite=1000
    )
    print(f"   • Vente: 1000L Gasoil → {formater_montant(transaction1.total)}")
    
    # Vente 2: 500L d'Essence Super à Station AFRIQUIA
    transaction2 = Transaction.enregistrer_transaction(
        type="vente",
        type_entite="client",
        entite_id=clients[1].id,  # Station AFRIQUIA
        produit_id=produits[1].id,  # Essence Super
        quantite=500
    )
    print(f"   • Vente: 500L Essence Super → {formater_montant(transaction2.total)}")
    
    # Achat: 2000L de Gasoil de SAMIR
    transaction3 = Transaction.enregistrer_transaction(
        type="achat",
        type_entite="fournisseur",
        entite_id=fournisseurs[0].id,  # SAMIR
        produit_id=produits[0].id,  # Gasoil
        quantite=2000
    )
    print(f"   • Achat: 2000L Gasoil → {formater_montant(transaction3.total)}")
    
    print("✅ 3 transactions créées")
    
    # Vérifier les stocks
    print("\n8️⃣ Vérification des stocks...")
    for produit in Produit.get_all():
        print(f"   • {produit.nom}: {produit.quantite} L (Valeur: {formater_montant(produit.calculer_valeur_stock())})")
    
    # Vérifier les soldes
    print("\n9️⃣ Vérification des soldes...")
    print("   Clients:")
    for client in Client.get_all():
        if client.solde > 0:
            print(f"   • {client.nom}: {formater_montant(client.solde)}")
    
    print("   Fournisseurs:")
    for fournisseur in Fournisseur.get_all():
        if fournisseur.solde < 0:
            print(f"   • {fournisseur.nom}: {formater_montant(abs(fournisseur.solde))} (dette)")
    
    # Créer des déclarations
    print("\n🔟 Création de déclarations fiscales...")
    
    # TVA mensuelle
    tva_declaration = declarer_impot("TVA", "2024-12", 5000.0)
    print(f"   • Déclaration TVA: {formater_montant(tva_declaration.montant)}")
    
    # IS annuel
    is_declaration = declarer_impot("IS", "2024", 25000.0)
    print(f"   • Déclaration IS: {formater_montant(is_declaration.montant)}")
    
    # Déclarations sociales
    print("\n1️⃣1️⃣ Génération des déclarations sociales...")
    for employe in employes:
        declaration_sociale = DeclarationSociale(
            employe_id=employe.id,
            mois="2024-12",
            cnss=employe.cnss,
            amo=employe.amo,
            ir=employe.ir
        )
        declaration_sociale.save()
        print(f"   • {employe.nom}: {formater_montant(declaration_sociale.total)}")
    
    # Statistiques finales
    print("\n1️⃣2️⃣ Statistiques finales...")
    
    nb_clients = len(Client.get_all())
    nb_fournisseurs = len(Fournisseur.get_all())
    nb_produits = len(Produit.get_all())
    nb_employes = len(Employe.get_all())
    nb_transactions = len(Transaction.get_all())
    
    print(f"   • Clients: {nb_clients}")
    print(f"   • Fournisseurs: {nb_fournisseurs}")
    print(f"   • Produits: {nb_produits}")
    print(f"   • Employés: {nb_employes}")
    print(f"   • Transactions: {nb_transactions}")
    
    # Valeur totale du stock
    valeur_stock = sum(p.calculer_valeur_stock() for p in Produit.get_all())
    print(f"   • Valeur totale du stock: {formater_montant(valeur_stock)}")
    
    # Masse salariale
    masse = Employe.calculer_masse_salariale()
    print(f"   • Masse salariale mensuelle: {formater_montant(masse['total_brut'])}")
    
    # Test des rapports
    print("\n1️⃣3️⃣ Test des fonctions de rapport...")
    
    # Rapport mensuel (données actuelles)
    mois_actuel = datetime.now().strftime("%Y-%m")
    try:
        rapport = generer_rapport_mensuel(mois_actuel)
        print(f"   • Rapport mensuel {mois_actuel}: ✅")
        print(f"     - CA TTC: {formater_montant(rapport['chiffre_affaires']['chiffre_affaires_ttc'])}")
        print(f"     - Nombre transactions: {rapport['nombre_transactions']}")
    except Exception as e:
        print(f"   • Erreur rapport mensuel: {e}")
    
    # Bilan annuel
    annee_actuelle = datetime.now().year
    try:
        bilan = generer_bilan_annuel(annee_actuelle)
        print(f"   • Bilan annuel {annee_actuelle}: ✅")
        print(f"     - CA annuel: {formater_montant(bilan['chiffre_affaires_annuel']['chiffre_affaires_ttc'])}")
    except Exception as e:
        print(f"   • Erreur bilan annuel: {e}")
    
    print("\n🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS!")
    print("=" * 50)
    print("✅ Toutes les fonctionnalités ont été testées")
    print("✅ Base de données créée dans: data/fuel_company.db")
    print("✅ Vous pouvez maintenant lancer: python main.py")
    
    # Fermer la base
    db.close()

if __name__ == "__main__":
    demo_complete()
