"""
Configuration et initialisation de la base de données SQLite
pour le système de comptabilité COMTABELITE
"""

import sqlite3
import os
from typing import Optional

class Database:
    """Gestionnaire de base de données SQLite pour COMTABELITE"""
    
    def __init__(self, db_path: str = "data/fuel_company.db"):
        """
        Initialise la connexion à la base de données
        
        Args:
            db_path: Chemin vers le fichier de base de données
        """
        self.db_path = db_path
        self._ensure_data_directory()
        self.connection: Optional[sqlite3.Connection] = None
        
    def _ensure_data_directory(self):
        """Crée le dossier data s'il n'existe pas"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def connect(self) -> sqlite3.Connection:
        """Établit la connexion à la base de données"""
        if self.connection is None:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Pour accéder aux colonnes par nom
        return self.connection
    
    def close(self):
        """Ferme la connexion à la base de données"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = ()) -> sqlite3.Cursor:
        """
        Exécute une requête SQL
        
        Args:
            query: Requête SQL à exécuter
            params: Paramètres de la requête
            
        Returns:
            Curseur de la base de données
        """
        conn = self.connect()
        return conn.execute(query, params)
    
    def execute_many(self, query: str, params_list: list) -> sqlite3.Cursor:
        """
        Exécute une requête SQL avec plusieurs jeux de paramètres
        
        Args:
            query: Requête SQL à exécuter
            params_list: Liste de tuples de paramètres
            
        Returns:
            Curseur de la base de données
        """
        conn = self.connect()
        return conn.executemany(query, params_list)
    
    def commit(self):
        """Valide les changements dans la base de données"""
        if self.connection:
            self.connection.commit()
    
    def rollback(self):
        """Annule les changements non validés"""
        if self.connection:
            self.connection.rollback()
    
    def create_tables(self):
        """Crée toutes les tables nécessaires pour le système"""
        
        # Table des clients
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                telephone TEXT,
                adresse TEXT,
                solde REAL DEFAULT 0.0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Table des fournisseurs
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                telephone TEXT,
                adresse TEXT,
                solde REAL DEFAULT 0.0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Table des produits (carburants)
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prix_achat REAL NOT NULL,
                prix_vente REAL NOT NULL,
                quantite REAL DEFAULT 0.0,
                unite TEXT DEFAULT 'L',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Table des transactions
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT NOT NULL CHECK (type IN ('achat', 'vente')),
                type_entite TEXT NOT NULL CHECK (type_entite IN ('client', 'fournisseur')),
                entite_id INTEGER NOT NULL,
                produit_id INTEGER NOT NULL,
                quantite REAL NOT NULL,
                prix_unitaire REAL NOT NULL,
                total REAL NOT NULL,
                tva REAL DEFAULT 0.0,
                date_transaction TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (produit_id) REFERENCES produits (id)
            )
        """)
        
        # Table des employés
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS employes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                cin TEXT UNIQUE NOT NULL,
                numero_cnss TEXT,
                salaire_brut REAL NOT NULL,
                salaire_net REAL,
                cnss REAL,
                amo REAL,
                ir REAL,
                date_embauche TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Table des déclarations fiscales
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS declarations_fiscales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_impot TEXT NOT NULL CHECK (type_impot IN ('TVA', 'IS', 'IR')),
                periode TEXT NOT NULL,
                montant REAL NOT NULL,
                date_declaration TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Table des déclarations sociales
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS declarations_sociales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employe_id INTEGER NOT NULL,
                mois TEXT NOT NULL,
                cnss REAL NOT NULL,
                amo REAL NOT NULL,
                ir REAL NOT NULL,
                total REAL NOT NULL,
                date_declaration TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employe_id) REFERENCES employes (id)
            )
        """)
        
        self.commit()
        print("✅ Tables créées avec succès!")

# Instance globale de la base de données
db = Database()
