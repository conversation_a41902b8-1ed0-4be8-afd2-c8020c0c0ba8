"""
Modèle Transaction pour le système de comptabilité COMTABELITE
Gestion des achats et ventes de carburants
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from .database import db
from .client import Client
from .fournisseur import Fournisseur
from .produit import Produit

class Transaction:
    """Modèle pour gérer les transactions d'achat et de vente"""

    def __init__(self, id: Optional[int] = None, type: str = "", type_entite: str = "",
                 entite_id: int = 0, produit_id: int = 0, quantite: float = 0.0,
                 prix_unitaire: float = 0.0, total: float = 0.0, tva: float = 0.0,
                 date_transaction: Optional[datetime] = None):
        """
        Initialise une transaction

        Args:
            id: Identifiant unique de la transaction
            type: Type de transaction ('achat' ou 'vente')
            type_entite: Type d'entité ('client' ou 'fournisseur')
            entite_id: ID du client ou fournisseur
            produit_id: ID du produit
            quantite: Quantité transactionnée
            prix_unitaire: Prix unitaire de la transaction
            total: Montant total de la transaction
            tva: Montant de la TVA
            date_transaction: Date de la transaction
        """
        self.id = id
        self.type = type
        self.type_entite = type_entite
        self.entite_id = entite_id
        self.produit_id = produit_id
        self.quantite = quantite
        self.prix_unitaire = prix_unitaire
        self.total = total
        self.tva = tva
        self.date_transaction = date_transaction or datetime.now()

    def save(self) -> int:
        """
        Sauvegarde la transaction dans la base de données

        Returns:
            ID de la transaction créée
        """
        if self.id is None:
            # Nouvelle transaction
            cursor = db.execute_query(
                """INSERT INTO transactions
                   (type, type_entite, entite_id, produit_id, quantite, prix_unitaire, total, tva, date_transaction)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (self.type, self.type_entite, self.entite_id, self.produit_id,
                 self.quantite, self.prix_unitaire, self.total, self.tva, self.date_transaction)
            )
            self.id = cursor.lastrowid
            db.commit()
            print(f"✅ Transaction {self.type} créée avec l'ID {self.id}")
        else:
            # Mise à jour transaction existante
            db.execute_query(
                """UPDATE transactions SET type=?, type_entite=?, entite_id=?, produit_id=?,
                   quantite=?, prix_unitaire=?, total=?, tva=?, date_transaction=? WHERE id=?""",
                (self.type, self.type_entite, self.entite_id, self.produit_id,
                 self.quantite, self.prix_unitaire, self.total, self.tva, self.date_transaction, self.id)
            )
            db.commit()
            print(f"✅ Transaction {self.type} mise à jour")

        return self.id

    def process_transaction(self):
        """
        Traite la transaction en mettant à jour les stocks et soldes
        """
        # Récupérer le produit
        produit = Produit.get_by_id(self.produit_id)
        if not produit:
            raise ValueError(f"Produit avec ID {self.produit_id} non trouvé")

        # Traitement selon le type de transaction
        if self.type == "achat":
            # Achat: augmenter le stock, augmenter la dette fournisseur
            produit.mettre_a_jour_stock(self.quantite, "ajouter")

            if self.type_entite == "fournisseur":
                fournisseur = Fournisseur.get_by_id(self.entite_id)
                if fournisseur:
                    # Augmenter la dette (solde négatif)
                    fournisseur.mettre_a_jour_solde(self.total, "soustraire")

        elif self.type == "vente":
            # Vente: diminuer le stock, augmenter le crédit client
            produit.mettre_a_jour_stock(self.quantite, "soustraire")

            if self.type_entite == "client":
                client = Client.get_by_id(self.entite_id)
                if client:
                    # Augmenter le crédit (solde positif)
                    client.mettre_a_jour_solde(self.total, "ajouter")

        # Ne pas sauvegarder ici car c'est fait dans enregistrer_transaction

    @classmethod
    def enregistrer_transaction(cls, type: str, type_entite: str, entite_id: int,
                              produit_id: int, quantite: float, prix_unitaire: float = None,
                              taux_tva: float = 0.20) -> 'Transaction':
        """
        Enregistre une nouvelle transaction avec calculs automatiques

        Args:
            type: 'achat' ou 'vente'
            type_entite: 'client' ou 'fournisseur'
            entite_id: ID de l'entité
            produit_id: ID du produit
            quantite: Quantité
            prix_unitaire: Prix unitaire (optionnel, utilise le prix du produit si non fourni)
            taux_tva: Taux de TVA (20% par défaut au Maroc)

        Returns:
            Instance de la transaction créée
        """
        # Récupérer le produit pour les prix
        produit = Produit.get_by_id(produit_id)
        if not produit:
            raise ValueError(f"Produit avec ID {produit_id} non trouvé")

        # Déterminer le prix unitaire
        if prix_unitaire is None:
            if type == "achat":
                prix_unitaire = produit.prix_achat
            else:  # vente
                prix_unitaire = produit.prix_vente

        # Calculer le total HT et la TVA
        total_ht = quantite * prix_unitaire
        tva = total_ht * taux_tva
        total_ttc = total_ht + tva

        # Créer la transaction
        transaction = cls(
            type=type,
            type_entite=type_entite,
            entite_id=entite_id,
            produit_id=produit_id,
            quantite=quantite,
            prix_unitaire=prix_unitaire,
            total=total_ttc,
            tva=tva
        )

        # Sauvegarder d'abord la transaction
        transaction.save()

        # Puis traiter les mises à jour de stock et soldes
        transaction.process_transaction()

        return transaction

    @classmethod
    def get_by_id(cls, transaction_id: int) -> Optional['Transaction']:
        """
        Récupère une transaction par son ID

        Args:
            transaction_id: ID de la transaction à récupérer

        Returns:
            Instance Transaction ou None si non trouvée
        """
        cursor = db.execute_query("SELECT * FROM transactions WHERE id=?", (transaction_id,))
        row = cursor.fetchone()

        if row:
            return cls(
                id=row['id'],
                type=row['type'],
                type_entite=row['type_entite'],
                entite_id=row['entite_id'],
                produit_id=row['produit_id'],
                quantite=row['quantite'],
                prix_unitaire=row['prix_unitaire'],
                total=row['total'],
                tva=row['tva'],
                date_transaction=datetime.fromisoformat(row['date_transaction'])
            )
        return None

    @classmethod
    def get_all(cls) -> List['Transaction']:
        """
        Récupère toutes les transactions

        Returns:
            Liste de toutes les transactions
        """
        cursor = db.execute_query("SELECT * FROM transactions ORDER BY date_transaction DESC")
        transactions = []

        for row in cursor.fetchall():
            transactions.append(cls(
                id=row['id'],
                type=row['type'],
                type_entite=row['type_entite'],
                entite_id=row['entite_id'],
                produit_id=row['produit_id'],
                quantite=row['quantite'],
                prix_unitaire=row['prix_unitaire'],
                total=row['total'],
                tva=row['tva'],
                date_transaction=datetime.fromisoformat(row['date_transaction'])
            ))

        return transactions

    @classmethod
    def get_by_period(cls, debut: datetime, fin: datetime) -> List['Transaction']:
        """
        Récupère les transactions d'une période donnée

        Args:
            debut: Date de début
            fin: Date de fin

        Returns:
            Liste des transactions de la période
        """
        cursor = db.execute_query(
            "SELECT * FROM transactions WHERE date_transaction BETWEEN ? AND ? ORDER BY date_transaction DESC",
            (debut.isoformat(), fin.isoformat())
        )
        transactions = []

        for row in cursor.fetchall():
            transactions.append(cls(
                id=row['id'],
                type=row['type'],
                type_entite=row['type_entite'],
                entite_id=row['entite_id'],
                produit_id=row['produit_id'],
                quantite=row['quantite'],
                prix_unitaire=row['prix_unitaire'],
                total=row['total'],
                tva=row['tva'],
                date_transaction=datetime.fromisoformat(row['date_transaction'])
            ))

        return transactions

    @classmethod
    def get_by_type(cls, type_transaction: str) -> List['Transaction']:
        """
        Récupère les transactions par type

        Args:
            type_transaction: 'achat' ou 'vente'

        Returns:
            Liste des transactions du type spécifié
        """
        cursor = db.execute_query(
            "SELECT * FROM transactions WHERE type=? ORDER BY date_transaction DESC",
            (type_transaction,)
        )
        transactions = []

        for row in cursor.fetchall():
            transactions.append(cls(
                id=row['id'],
                type=row['type'],
                type_entite=row['type_entite'],
                entite_id=row['entite_id'],
                produit_id=row['produit_id'],
                quantite=row['quantite'],
                prix_unitaire=row['prix_unitaire'],
                total=row['total'],
                tva=row['tva'],
                date_transaction=datetime.fromisoformat(row['date_transaction'])
            ))

        return transactions

    def get_entite_nom(self) -> str:
        """
        Récupère le nom de l'entité (client ou fournisseur)

        Returns:
            Nom de l'entité
        """
        if self.type_entite == "client":
            client = Client.get_by_id(self.entite_id)
            return client.nom if client else "Client inconnu"
        elif self.type_entite == "fournisseur":
            fournisseur = Fournisseur.get_by_id(self.entite_id)
            return fournisseur.nom if fournisseur else "Fournisseur inconnu"
        return "Entité inconnue"

    def get_produit_nom(self) -> str:
        """
        Récupère le nom du produit

        Returns:
            Nom du produit
        """
        produit = Produit.get_by_id(self.produit_id)
        return produit.nom if produit else "Produit inconnu"

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit la transaction en dictionnaire

        Returns:
            Dictionnaire représentant la transaction
        """
        return {
            'id': self.id,
            'type': self.type,
            'type_entite': self.type_entite,
            'entite_id': self.entite_id,
            'entite_nom': self.get_entite_nom(),
            'produit_id': self.produit_id,
            'produit_nom': self.get_produit_nom(),
            'quantite': self.quantite,
            'prix_unitaire': self.prix_unitaire,
            'total': self.total,
            'tva': self.tva,
            'date_transaction': self.date_transaction.isoformat()
        }

    def __str__(self) -> str:
        """Représentation string de la transaction"""
        return (f"Transaction(ID: {self.id}, Type: {self.type}, "
                f"Entité: {self.get_entite_nom()}, Produit: {self.get_produit_nom()}, "
                f"Total: {self.total:.2f} DH)")

    def __repr__(self) -> str:
        """Représentation détaillée de la transaction"""
        return (f"Transaction(id={self.id}, type='{self.type}', type_entite='{self.type_entite}', "
                f"entite_id={self.entite_id}, produit_id={self.produit_id}, quantite={self.quantite}, "
                f"prix_unitaire={self.prix_unitaire}, total={self.total}, tva={self.tva})")
