"""
Fonctions de calcul pour le système de comptabilité COMTABELITE
Calculs de salaires, impôts et cotisations selon la législation marocaine
"""

from typing import Dict, List
from datetime import datetime, timedelta
from models.transaction import Transaction
from models.employe import Employe
from models.declaration import DeclarationFiscale, DeclarationSociale

def calculer_salaire(salaire_base: float) -> Dict[str, float]:
    """
    Calcule le salaire net et les déductions selon la législation marocaine
    
    Args:
        salaire_base: Salaire brut de base
        
    Returns:
        Dictionnaire avec les détails du calcul
    """
    # Taux de cotisations au Maroc (2024)
    TAUX_CNSS_EMPLOYE = 0.0448  # 4.48%
    TAUX_AMO_EMPLOYE = 0.0226   # 2.26%
    
    # Calcul CNSS (plafonné à 6000 DH)
    base_cnss = min(salaire_base, 6000)
    cnss = base_cnss * TAUX_CNSS_EMPLOYE
    
    # Calcul AMO (plafonné à 5000 DH)
    base_amo = min(salaire_base, 5000)
    amo = base_amo * TAUX_AMO_EMPLOYE
    
    # Calcul IR (Impôt sur le Revenu)
    ir = calculer_ir(salaire_base, cnss, amo)
    
    # Salaire net
    salaire_net = salaire_base - cnss - amo - ir
    
    return {
        'salaire_brut': salaire_base,
        'cnss': cnss,
        'amo': amo,
        'ir': ir,
        'total_deductions': cnss + amo + ir,
        'salaire_net': salaire_net
    }

def calculer_ir(salaire_brut: float, cnss: float, amo: float) -> float:
    """
    Calcule l'impôt sur le revenu selon le barème marocain
    
    Args:
        salaire_brut: Salaire brut
        cnss: Cotisation CNSS
        amo: Cotisation AMO
        
    Returns:
        Montant de l'IR
    """
    # Salaire imposable = Salaire brut - CNSS - AMO
    salaire_imposable = salaire_brut - cnss - amo
    
    # Barème IR 2024 (mensuel)
    if salaire_imposable <= 2500:
        return 0
    elif salaire_imposable <= 4166.67:
        return (salaire_imposable - 2500) * 0.10
    elif salaire_imposable <= 5000:
        return 166.67 + (salaire_imposable - 4166.67) * 0.20
    elif salaire_imposable <= 6666.67:
        return 333.33 + (salaire_imposable - 5000) * 0.30
    elif salaire_imposable <= 15000:
        return 833.33 + (salaire_imposable - 6666.67) * 0.34
    else:
        return 3666.67 + (salaire_imposable - 15000) * 0.38

def calculer_tva_periode(debut: datetime, fin: datetime) -> Dict[str, float]:
    """
    Calcule la TVA collectée et déductible pour une période donnée
    
    Args:
        debut: Date de début de la période
        fin: Date de fin de la période
        
    Returns:
        Dictionnaire avec les montants de TVA
    """
    transactions = Transaction.get_by_period(debut, fin)
    
    tva_collectee = 0.0  # TVA sur les ventes
    tva_deductible = 0.0  # TVA sur les achats
    
    for transaction in transactions:
        if transaction.type == "vente":
            tva_collectee += transaction.tva
        elif transaction.type == "achat":
            tva_deductible += transaction.tva
    
    tva_a_payer = tva_collectee - tva_deductible
    
    return {
        'periode_debut': debut.strftime('%Y-%m-%d'),
        'periode_fin': fin.strftime('%Y-%m-%d'),
        'tva_collectee': tva_collectee,
        'tva_deductible': tva_deductible,
        'tva_a_payer': max(0, tva_a_payer),  # Ne peut pas être négative
        'credit_tva': max(0, -tva_a_payer)   # Crédit si TVA déductible > collectée
    }

def calculer_chiffre_affaires_periode(debut: datetime, fin: datetime) -> Dict[str, float]:
    """
    Calcule le chiffre d'affaires pour une période donnée
    
    Args:
        debut: Date de début de la période
        fin: Date de fin de la période
        
    Returns:
        Dictionnaire avec les montants
    """
    transactions = Transaction.get_by_period(debut, fin)
    
    ca_ht = 0.0
    ca_ttc = 0.0
    achats_ht = 0.0
    achats_ttc = 0.0
    
    for transaction in transactions:
        montant_ht = transaction.total - transaction.tva
        
        if transaction.type == "vente":
            ca_ht += montant_ht
            ca_ttc += transaction.total
        elif transaction.type == "achat":
            achats_ht += montant_ht
            achats_ttc += transaction.total
    
    benefice_brut = ca_ht - achats_ht
    
    return {
        'periode_debut': debut.strftime('%Y-%m-%d'),
        'periode_fin': fin.strftime('%Y-%m-%d'),
        'chiffre_affaires_ht': ca_ht,
        'chiffre_affaires_ttc': ca_ttc,
        'achats_ht': achats_ht,
        'achats_ttc': achats_ttc,
        'benefice_brut': benefice_brut
    }

def calculer_is_annuel(annee: int) -> Dict[str, float]:
    """
    Calcule l'Impôt sur les Sociétés pour une année donnée
    
    Args:
        annee: Année de calcul
        
    Returns:
        Dictionnaire avec le calcul de l'IS
    """
    debut = datetime(annee, 1, 1)
    fin = datetime(annee, 12, 31, 23, 59, 59)
    
    # Chiffre d'affaires et charges
    ca_data = calculer_chiffre_affaires_periode(debut, fin)
    
    # Charges salariales
    employes = Employe.get_all()
    charges_salariales = sum(emp.salaire_brut * 12 for emp in employes)
    
    # Résultat avant impôt
    resultat_brut = ca_data['benefice_brut']
    resultat_net = resultat_brut - charges_salariales
    
    # Calcul IS (taux standard 31% au Maroc)
    taux_is = 0.31
    is_a_payer = max(0, resultat_net * taux_is)
    
    return {
        'annee': annee,
        'chiffre_affaires': ca_data['chiffre_affaires_ht'],
        'achats': ca_data['achats_ht'],
        'charges_salariales': charges_salariales,
        'resultat_brut': resultat_brut,
        'resultat_net': resultat_net,
        'taux_is': taux_is,
        'is_a_payer': is_a_payer
    }

def generer_declarations_sociales_mois(mois: str) -> List[DeclarationSociale]:
    """
    Génère les déclarations sociales pour tous les employés d'un mois donné
    
    Args:
        mois: Mois au format YYYY-MM
        
    Returns:
        Liste des déclarations sociales créées
    """
    employes = Employe.get_all()
    declarations = []
    
    for employe in employes:
        # Vérifier si la déclaration existe déjà
        declarations_existantes = DeclarationSociale.get_by_employe(employe.id)
        existe = any(decl.mois == mois for decl in declarations_existantes)
        
        if not existe:
            declaration = DeclarationSociale(
                employe_id=employe.id,
                mois=mois,
                cnss=employe.cnss,
                amo=employe.amo,
                ir=employe.ir
            )
            declaration.save()
            declarations.append(declaration)
    
    return declarations

def enregistrer_transaction(type: str, entite: str, produit_id: int, quantite: float, 
                          prix_unitaire: float = None) -> Transaction:
    """
    Enregistre une nouvelle transaction avec mise à jour automatique des stocks et soldes
    
    Args:
        type: Type de transaction ('achat' ou 'vente')
        entite: Type d'entité ('client' ou 'fournisseur')
        produit_id: ID du produit
        quantite: Quantité
        prix_unitaire: Prix unitaire (optionnel)
        
    Returns:
        Transaction créée
    """
    # Cette fonction sera implémentée dans le modèle Transaction
    # Ici on fait juste un wrapper pour respecter les spécifications
    pass

def declarer_impot(type: str, periode: str, montant: float) -> DeclarationFiscale:
    """
    Crée une déclaration fiscale
    
    Args:
        type: Type d'impôt ('TVA', 'IS', 'IR')
        periode: Période de la déclaration
        montant: Montant de l'impôt
        
    Returns:
        Déclaration fiscale créée
    """
    return DeclarationFiscale.declarer_impot(type, periode, montant)

def calculer_cotisations_patronales(salaire_brut: float) -> Dict[str, float]:
    """
    Calcule les cotisations patronales selon la législation marocaine
    
    Args:
        salaire_brut: Salaire brut de l'employé
        
    Returns:
        Dictionnaire avec les cotisations patronales
    """
    # Taux de cotisations patronales au Maroc (2024)
    TAUX_CNSS_PATRON = 0.2093   # 20.93%
    TAUX_AMO_PATRON = 0.0226    # 2.26%
    TAUX_FORMATION = 0.016      # 1.6%
    
    # Calcul CNSS patronale (plafonné à 6000 DH)
    base_cnss = min(salaire_brut, 6000)
    cnss_patron = base_cnss * TAUX_CNSS_PATRON
    
    # Calcul AMO patronale (plafonné à 5000 DH)
    base_amo = min(salaire_brut, 5000)
    amo_patron = base_amo * TAUX_AMO_PATRON
    
    # Taxe de formation professionnelle
    formation = salaire_brut * TAUX_FORMATION
    
    total_charges = cnss_patron + amo_patron + formation
    
    return {
        'cnss_patron': cnss_patron,
        'amo_patron': amo_patron,
        'formation': formation,
        'total_charges_patronales': total_charges,
        'cout_total_employe': salaire_brut + total_charges
    }
