#!/usr/bin/env python3
"""
COMTABELITE - Système de comptabilité pour entreprise de carburants
Interface en ligne de commande principale

Auteur: Système de comptabilité COMTABELITE
Version: 1.0
"""

import sys
import os
from datetime import datetime

# Ajouter le répertoire racine au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import db
from models.client import Client
from models.fournisseur import Fournisseur
from models.produit import Produit
from models.transaction import Transaction
from models.employe import Employe
from models.declaration import DeclarationFiscale, DeclarationSociale

from utils.calculs import (
    calculer_salaire, declarer_impot, generer_declarations_sociales_mois,
    calculer_tva_periode, calculer_is_annuel
)
from utils.rapports import generer_rapport_mensuel, generer_bilan_annuel, afficher_rapport_mensuel, afficher_bilan_annuel
from utils.helpers import (
    saisir_choix, confirmer_action, valider_montant, valider_quantite,
    valider_cin, valider_telephone, afficher_tableau, afficher_logo,
    formater_montant, sauvegarder_backup
)

class ComptabiliteApp:
    """Application principale de comptabilité"""

    def __init__(self):
        """Initialise l'application"""
        self.running = True

    def demarrer(self):
        """Démarre l'application"""
        try:
            # Initialiser la base de données
            db.create_tables()

            # Afficher le logo
            afficher_logo()
            print("🚀 Bienvenue dans COMTABELITE!")
            print("Système de comptabilité pour entreprise de carburants\n")

            # Boucle principale
            while self.running:
                self.afficher_menu_principal()

        except KeyboardInterrupt:
            print("\n\n👋 Au revoir!")
        except Exception as e:
            print(f"\n❌ Erreur critique: {e}")
        finally:
            db.close()

    def afficher_menu_principal(self):
        """Affiche le menu principal"""
        options = [
            "Gestion des Clients",
            "Gestion des Fournisseurs",
            "Gestion des Produits",
            "Gestion des Employés",
            "Transactions",
            "Déclarations Fiscales",
            "Rapports et Analyses",
            "Utilitaires",
            "Quitter"
        ]

        choix = saisir_choix(options, "=== MENU PRINCIPAL ===")

        if choix == 0:
            self.menu_clients()
        elif choix == 1:
            self.menu_fournisseurs()
        elif choix == 2:
            self.menu_produits()
        elif choix == 3:
            self.menu_employes()
        elif choix == 4:
            self.menu_transactions()
        elif choix == 5:
            self.menu_declarations()
        elif choix == 6:
            self.menu_rapports()
        elif choix == 7:
            self.menu_utilitaires()
        elif choix == 8:
            self.quitter()

    def menu_clients(self):
        """Menu de gestion des clients"""
        options = [
            "Ajouter un client",
            "Lister tous les clients",
            "Rechercher un client",
            "Modifier un client",
            "Supprimer un client",
            "Voir les soldes clients",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== GESTION DES CLIENTS ===")

        if choix == 0:
            self.ajouter_client()
        elif choix == 1:
            self.lister_clients()
        elif choix == 2:
            self.rechercher_client()
        elif choix == 3:
            self.modifier_client()
        elif choix == 4:
            self.supprimer_client()
        elif choix == 5:
            self.voir_soldes_clients()
        # choix == 6: retour automatique

    def ajouter_client(self):
        """Ajoute un nouveau client"""
        print("\n=== AJOUTER UN CLIENT ===")

        nom = input("Nom du client: ").strip()
        if not nom:
            print("❌ Le nom est obligatoire")
            return

        telephone = input("Téléphone (optionnel): ").strip()
        if telephone and not valider_telephone(telephone):
            print("⚠️ Format de téléphone invalide, mais enregistré quand même")

        adresse = input("Adresse (optionnel): ").strip()

        solde_str = input("Solde initial (0 par défaut): ").strip()
        solde = valider_montant(solde_str) if solde_str else 0.0
        if solde is None:
            solde = 0.0

        client = Client(nom=nom, telephone=telephone, adresse=adresse, solde=solde)
        client.save()

    def lister_clients(self):
        """Liste tous les clients"""
        clients = Client.get_all()

        if not clients:
            print("\n📋 Aucun client enregistré")
            return

        donnees = []
        for client in clients:
            donnees.append({
                'ID': client.id,
                'Nom': client.nom,
                'Téléphone': client.telephone or 'N/A',
                'Solde': formater_montant(client.solde)
            })

        afficher_tableau(donnees, ['ID', 'Nom', 'Téléphone', 'Solde'], "LISTE DES CLIENTS")

    def rechercher_client(self):
        """Recherche un client par nom"""
        nom = input("\nNom à rechercher: ").strip()
        if not nom:
            print("❌ Veuillez saisir un nom")
            return

        clients = Client.search_by_name(nom)

        if not clients:
            print(f"❌ Aucun client trouvé avec '{nom}'")
            return

        donnees = []
        for client in clients:
            donnees.append({
                'ID': client.id,
                'Nom': client.nom,
                'Téléphone': client.telephone or 'N/A',
                'Adresse': client.adresse or 'N/A',
                'Solde': formater_montant(client.solde)
            })

        afficher_tableau(donnees, ['ID', 'Nom', 'Téléphone', 'Adresse', 'Solde'],
                        f"RÉSULTATS POUR '{nom}'")

    def modifier_client(self):
        """Modifie un client existant"""
        self.lister_clients()

        try:
            client_id = int(input("\nID du client à modifier: "))
            client = Client.get_by_id(client_id)

            if not client:
                print("❌ Client non trouvé")
                return

            print(f"\nModification du client: {client.nom}")
            print("(Laissez vide pour conserver la valeur actuelle)")

            nom = input(f"Nom ({client.nom}): ").strip()
            if nom:
                client.nom = nom

            telephone = input(f"Téléphone ({client.telephone}): ").strip()
            if telephone:
                if valider_telephone(telephone):
                    client.telephone = telephone
                else:
                    print("⚠️ Format invalide, téléphone non modifié")

            adresse = input(f"Adresse ({client.adresse}): ").strip()
            if adresse:
                client.adresse = adresse

            solde_str = input(f"Solde ({client.solde}): ").strip()
            if solde_str:
                solde = valider_montant(solde_str)
                if solde is not None:
                    client.solde = solde

            client.save()

        except ValueError:
            print("❌ ID invalide")

    def supprimer_client(self):
        """Supprime un client"""
        self.lister_clients()

        try:
            client_id = int(input("\nID du client à supprimer: "))
            client = Client.get_by_id(client_id)

            if not client:
                print("❌ Client non trouvé")
                return

            if confirmer_action(f"Supprimer le client '{client.nom}' ?"):
                client.delete()
            else:
                print("❌ Suppression annulée")

        except ValueError:
            print("❌ ID invalide")

    def voir_soldes_clients(self):
        """Affiche les soldes des clients"""
        clients = Client.get_soldes_positifs()

        if not clients:
            print("\n📋 Aucun client avec un solde positif")
            return

        donnees = []
        total_creances = 0

        for client in clients:
            donnees.append({
                'Nom': client.nom,
                'Téléphone': client.telephone or 'N/A',
                'Solde': formater_montant(client.solde)
            })
            total_creances += client.solde

        afficher_tableau(donnees, ['Nom', 'Téléphone', 'Solde'], "CRÉANCES CLIENTS")
        print(f"\n💰 Total des créances: {formater_montant(total_creances)}")

    def menu_fournisseurs(self):
        """Menu de gestion des fournisseurs"""
        options = [
            "Ajouter un fournisseur",
            "Lister tous les fournisseurs",
            "Rechercher un fournisseur",
            "Modifier un fournisseur",
            "Supprimer un fournisseur",
            "Voir les dettes fournisseurs",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== GESTION DES FOURNISSEURS ===")

        if choix == 0:
            self.ajouter_fournisseur()
        elif choix == 1:
            self.lister_fournisseurs()
        elif choix == 2:
            self.rechercher_fournisseur()
        elif choix == 3:
            self.modifier_fournisseur()
        elif choix == 4:
            self.supprimer_fournisseur()
        elif choix == 5:
            self.voir_dettes_fournisseurs()

    def ajouter_fournisseur(self):
        """Ajoute un nouveau fournisseur"""
        print("\n=== AJOUTER UN FOURNISSEUR ===")

        nom = input("Nom du fournisseur: ").strip()
        if not nom:
            print("❌ Le nom est obligatoire")
            return

        telephone = input("Téléphone (optionnel): ").strip()
        if telephone and not valider_telephone(telephone):
            print("⚠️ Format de téléphone invalide, mais enregistré quand même")

        adresse = input("Adresse (optionnel): ").strip()

        solde_str = input("Solde initial (0 par défaut): ").strip()
        solde = valider_montant(solde_str) if solde_str else 0.0
        if solde is None:
            solde = 0.0

        fournisseur = Fournisseur(nom=nom, telephone=telephone, adresse=adresse, solde=solde)
        fournisseur.save()

    def lister_fournisseurs(self):
        """Liste tous les fournisseurs"""
        fournisseurs = Fournisseur.get_all()

        if not fournisseurs:
            print("\n📋 Aucun fournisseur enregistré")
            return

        donnees = []
        for fournisseur in fournisseurs:
            donnees.append({
                'ID': fournisseur.id,
                'Nom': fournisseur.nom,
                'Téléphone': fournisseur.telephone or 'N/A',
                'Solde': formater_montant(fournisseur.solde)
            })

        afficher_tableau(donnees, ['ID', 'Nom', 'Téléphone', 'Solde'], "LISTE DES FOURNISSEURS")

    def rechercher_fournisseur(self):
        """Recherche un fournisseur par nom"""
        nom = input("\nNom à rechercher: ").strip()
        if not nom:
            print("❌ Veuillez saisir un nom")
            return

        fournisseurs = Fournisseur.search_by_name(nom)

        if not fournisseurs:
            print(f"❌ Aucun fournisseur trouvé avec '{nom}'")
            return

        donnees = []
        for fournisseur in fournisseurs:
            donnees.append({
                'ID': fournisseur.id,
                'Nom': fournisseur.nom,
                'Téléphone': fournisseur.telephone or 'N/A',
                'Adresse': fournisseur.adresse or 'N/A',
                'Solde': formater_montant(fournisseur.solde)
            })

        afficher_tableau(donnees, ['ID', 'Nom', 'Téléphone', 'Adresse', 'Solde'],
                        f"RÉSULTATS POUR '{nom}'")

    def modifier_fournisseur(self):
        """Modifie un fournisseur existant"""
        self.lister_fournisseurs()

        try:
            fournisseur_id = int(input("\nID du fournisseur à modifier: "))
            fournisseur = Fournisseur.get_by_id(fournisseur_id)

            if not fournisseur:
                print("❌ Fournisseur non trouvé")
                return

            print(f"\nModification du fournisseur: {fournisseur.nom}")
            print("(Laissez vide pour conserver la valeur actuelle)")

            nom = input(f"Nom ({fournisseur.nom}): ").strip()
            if nom:
                fournisseur.nom = nom

            telephone = input(f"Téléphone ({fournisseur.telephone}): ").strip()
            if telephone:
                if valider_telephone(telephone):
                    fournisseur.telephone = telephone
                else:
                    print("⚠️ Format invalide, téléphone non modifié")

            adresse = input(f"Adresse ({fournisseur.adresse}): ").strip()
            if adresse:
                fournisseur.adresse = adresse

            solde_str = input(f"Solde ({fournisseur.solde}): ").strip()
            if solde_str:
                solde = valider_montant(solde_str)
                if solde is not None:
                    fournisseur.solde = solde

            fournisseur.save()

        except ValueError:
            print("❌ ID invalide")

    def supprimer_fournisseur(self):
        """Supprime un fournisseur"""
        self.lister_fournisseurs()

        try:
            fournisseur_id = int(input("\nID du fournisseur à supprimer: "))
            fournisseur = Fournisseur.get_by_id(fournisseur_id)

            if not fournisseur:
                print("❌ Fournisseur non trouvé")
                return

            if confirmer_action(f"Supprimer le fournisseur '{fournisseur.nom}' ?"):
                fournisseur.delete()
            else:
                print("❌ Suppression annulée")

        except ValueError:
            print("❌ ID invalide")

    def voir_dettes_fournisseurs(self):
        """Affiche les dettes envers les fournisseurs"""
        fournisseurs = Fournisseur.get_dettes()

        if not fournisseurs:
            print("\n📋 Aucune dette envers les fournisseurs")
            return

        donnees = []
        total_dettes = 0

        for fournisseur in fournisseurs:
            donnees.append({
                'Nom': fournisseur.nom,
                'Téléphone': fournisseur.telephone or 'N/A',
                'Dette': formater_montant(abs(fournisseur.solde))
            })
            total_dettes += abs(fournisseur.solde)

        afficher_tableau(donnees, ['Nom', 'Téléphone', 'Dette'], "DETTES FOURNISSEURS")
        print(f"\n💸 Total des dettes: {formater_montant(total_dettes)}")

    def menu_produits(self):
        """Menu de gestion des produits"""
        options = [
            "Ajouter un produit",
            "Lister tous les produits",
            "Modifier un produit",
            "Supprimer un produit",
            "Voir l'état des stocks",
            "Produits en stock faible",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== GESTION DES PRODUITS ===")

        if choix == 0:
            self.ajouter_produit()
        elif choix == 1:
            self.lister_produits()
        elif choix == 2:
            self.modifier_produit()
        elif choix == 3:
            self.supprimer_produit()
        elif choix == 4:
            self.voir_etat_stocks()
        elif choix == 5:
            self.voir_stocks_faibles()

    def ajouter_produit(self):
        """Ajoute un nouveau produit"""
        print("\n=== AJOUTER UN PRODUIT ===")

        nom = input("Nom du produit (ex: Gasoil, Essence Super): ").strip()
        if not nom:
            print("❌ Le nom est obligatoire")
            return

        prix_achat_str = input("Prix d'achat unitaire: ").strip()
        prix_achat = valider_montant(prix_achat_str)
        if prix_achat is None:
            return

        prix_vente_str = input("Prix de vente unitaire: ").strip()
        prix_vente = valider_montant(prix_vente_str)
        if prix_vente is None:
            return

        if prix_vente <= prix_achat:
            if not confirmer_action("⚠️ Prix de vente ≤ prix d'achat. Continuer ?"):
                return

        quantite_str = input("Quantité initiale (0 par défaut): ").strip()
        quantite = valider_quantite(quantite_str) if quantite_str else 0.0
        if quantite is None:
            quantite = 0.0

        unite = input("Unité de mesure (L par défaut): ").strip() or "L"

        produit = Produit(
            nom=nom,
            prix_achat=prix_achat,
            prix_vente=prix_vente,
            quantite=quantite,
            unite=unite
        )
        produit.save()

        marge = produit.calculer_marge()
        print(f"✅ Marge calculée: {marge:.1f}%")

    def lister_produits(self):
        """Liste tous les produits"""
        produits = Produit.get_all()

        if not produits:
            print("\n📋 Aucun produit enregistré")
            return

        donnees = []
        for produit in produits:
            donnees.append({
                'ID': produit.id,
                'Nom': produit.nom,
                'Prix Achat': formater_montant(produit.prix_achat),
                'Prix Vente': formater_montant(produit.prix_vente),
                'Stock': f"{produit.quantite} {produit.unite}",
                'Marge': f"{produit.calculer_marge():.1f}%"
            })

        afficher_tableau(donnees, ['ID', 'Nom', 'Prix Achat', 'Prix Vente', 'Stock', 'Marge'],
                        "LISTE DES PRODUITS")

    def modifier_produit(self):
        """Modifie un produit existant"""
        self.lister_produits()

        try:
            produit_id = int(input("\nID du produit à modifier: "))
            produit = Produit.get_by_id(produit_id)

            if not produit:
                print("❌ Produit non trouvé")
                return

            print(f"\nModification du produit: {produit.nom}")
            print("(Laissez vide pour conserver la valeur actuelle)")

            nom = input(f"Nom ({produit.nom}): ").strip()
            if nom:
                produit.nom = nom

            prix_achat_str = input(f"Prix d'achat ({produit.prix_achat}): ").strip()
            if prix_achat_str:
                prix_achat = valider_montant(prix_achat_str)
                if prix_achat is not None:
                    produit.prix_achat = prix_achat

            prix_vente_str = input(f"Prix de vente ({produit.prix_vente}): ").strip()
            if prix_vente_str:
                prix_vente = valider_montant(prix_vente_str)
                if prix_vente is not None:
                    produit.prix_vente = prix_vente

            quantite_str = input(f"Quantité ({produit.quantite}): ").strip()
            if quantite_str:
                quantite = valider_quantite(quantite_str)
                if quantite is not None:
                    produit.quantite = quantite

            unite = input(f"Unité ({produit.unite}): ").strip()
            if unite:
                produit.unite = unite

            produit.save()

        except ValueError:
            print("❌ ID invalide")

    def supprimer_produit(self):
        """Supprime un produit"""
        self.lister_produits()

        try:
            produit_id = int(input("\nID du produit à supprimer: "))
            produit = Produit.get_by_id(produit_id)

            if not produit:
                print("❌ Produit non trouvé")
                return

            if confirmer_action(f"Supprimer le produit '{produit.nom}' ?"):
                produit.delete()
            else:
                print("❌ Suppression annulée")

        except ValueError:
            print("❌ ID invalide")

    def voir_etat_stocks(self):
        """Affiche l'état détaillé des stocks"""
        produits = Produit.get_all()

        if not produits:
            print("\n📋 Aucun produit en stock")
            return

        donnees = []
        valeur_totale = 0

        for produit in produits:
            valeur_stock = produit.calculer_valeur_stock()
            valeur_totale += valeur_stock

            donnees.append({
                'Produit': produit.nom,
                'Quantité': f"{produit.quantite} {produit.unite}",
                'Prix Achat': formater_montant(produit.prix_achat),
                'Valeur Stock': formater_montant(valeur_stock)
            })

        afficher_tableau(donnees, ['Produit', 'Quantité', 'Prix Achat', 'Valeur Stock'],
                        "ÉTAT DES STOCKS")
        print(f"\n💰 Valeur totale du stock: {formater_montant(valeur_totale)}")

    def voir_stocks_faibles(self):
        """Affiche les produits en stock faible"""
        seuil_str = input("Seuil d'alerte (100 par défaut): ").strip()
        seuil = float(seuil_str) if seuil_str else 100.0

        produits = Produit.get_stock_faible(seuil)

        if not produits:
            print(f"\n✅ Aucun produit en dessous du seuil de {seuil}")
            return

        donnees = []
        for produit in produits:
            donnees.append({
                'Produit': produit.nom,
                'Stock Actuel': f"{produit.quantite} {produit.unite}",
                'Seuil': f"{seuil} {produit.unite}",
                'Statut': '🔴 CRITIQUE' if produit.quantite < seuil/2 else '🟡 FAIBLE'
            })

        afficher_tableau(donnees, ['Produit', 'Stock Actuel', 'Seuil', 'Statut'],
                        "STOCKS FAIBLES")

    def menu_employes(self):
        """Menu de gestion des employés"""
        options = [
            "Ajouter un employé",
            "Lister tous les employés",
            "Modifier un employé",
            "Supprimer un employé",
            "Calculer les salaires",
            "Voir la masse salariale",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== GESTION DES EMPLOYÉS ===")

        if choix == 0:
            self.ajouter_employe()
        elif choix == 1:
            self.lister_employes()
        elif choix == 2:
            self.modifier_employe()
        elif choix == 3:
            self.supprimer_employe()
        elif choix == 4:
            self.calculer_salaires()
        elif choix == 5:
            self.voir_masse_salariale()

    def ajouter_employe(self):
        """Ajoute un nouveau employé"""
        print("\n=== AJOUTER UN EMPLOYÉ ===")

        nom = input("Nom complet: ").strip()
        if not nom:
            print("❌ Le nom est obligatoire")
            return

        cin = input("CIN: ").strip()
        if not cin:
            print("❌ La CIN est obligatoire")
            return

        if not valider_cin(cin):
            print("⚠️ Format de CIN invalide, mais enregistré quand même")

        # Vérifier si la CIN existe déjà
        employe_existant = Employe.get_by_cin(cin)
        if employe_existant:
            print("❌ Un employé avec cette CIN existe déjà")
            return

        numero_cnss = input("Numéro CNSS (optionnel): ").strip()

        salaire_str = input("Salaire brut mensuel: ").strip()
        salaire_brut = valider_montant(salaire_str)
        if salaire_brut is None:
            return

        employe = Employe(
            nom=nom,
            cin=cin,
            numero_cnss=numero_cnss,
            salaire_brut=salaire_brut
        )

        # Calculer et afficher le détail du salaire
        calcul = employe.calculer_salaire()
        print(f"\n📊 DÉTAIL DU SALAIRE:")
        print(f"   • Salaire brut: {formater_montant(calcul['salaire_brut'])}")
        print(f"   • CNSS: {formater_montant(calcul['cnss'])}")
        print(f"   • AMO: {formater_montant(calcul['amo'])}")
        print(f"   • IR: {formater_montant(calcul['ir'])}")
        print(f"   • Total déductions: {formater_montant(calcul['total_deductions'])}")
        print(f"   • Salaire net: {formater_montant(calcul['salaire_net'])}")

        if confirmer_action("Confirmer l'ajout de cet employé ?"):
            employe.save()

    def lister_employes(self):
        """Liste tous les employés"""
        employes = Employe.get_all()

        if not employes:
            print("\n📋 Aucun employé enregistré")
            return

        donnees = []
        for employe in employes:
            donnees.append({
                'ID': employe.id,
                'Nom': employe.nom,
                'CIN': employe.cin,
                'Salaire Brut': formater_montant(employe.salaire_brut),
                'Salaire Net': formater_montant(employe.salaire_net)
            })

        afficher_tableau(donnees, ['ID', 'Nom', 'CIN', 'Salaire Brut', 'Salaire Net'],
                        "LISTE DES EMPLOYÉS")

    def modifier_employe(self):
        """Modifie un employé existant"""
        self.lister_employes()

        try:
            employe_id = int(input("\nID de l'employé à modifier: "))
            employe = Employe.get_by_id(employe_id)

            if not employe:
                print("❌ Employé non trouvé")
                return

            print(f"\nModification de l'employé: {employe.nom}")
            print("(Laissez vide pour conserver la valeur actuelle)")

            nom = input(f"Nom ({employe.nom}): ").strip()
            if nom:
                employe.nom = nom

            salaire_str = input(f"Salaire brut ({employe.salaire_brut}): ").strip()
            if salaire_str:
                salaire = valider_montant(salaire_str)
                if salaire is not None:
                    employe.salaire_brut = salaire

            employe.save()

        except ValueError:
            print("❌ ID invalide")

    def supprimer_employe(self):
        """Supprime un employé"""
        self.lister_employes()

        try:
            employe_id = int(input("\nID de l'employé à supprimer: "))
            employe = Employe.get_by_id(employe_id)

            if not employe:
                print("❌ Employé non trouvé")
                return

            if confirmer_action(f"Supprimer l'employé '{employe.nom}' ?"):
                employe.delete()
            else:
                print("❌ Suppression annulée")

        except ValueError:
            print("❌ ID invalide")

    def calculer_salaires(self):
        """Recalcule tous les salaires"""
        employes = Employe.get_all()

        if not employes:
            print("\n📋 Aucun employé enregistré")
            return

        print("\n🔄 Recalcul des salaires...")
        for employe in employes:
            employe.calculer_salaire()
            employe.save()

        print("✅ Salaires recalculés")
        self.lister_employes()

    def voir_masse_salariale(self):
        """Affiche la masse salariale totale"""
        masse = Employe.calculer_masse_salariale()

        print(f"\n💼 MASSE SALARIALE:")
        print(f"   • Nombre d'employés: {masse['nombre_employes']}")
        print(f"   • Total salaires bruts: {formater_montant(masse['total_brut'])}")
        print(f"   • Total salaires nets: {formater_montant(masse['total_net'])}")
        print(f"   • Total CNSS: {formater_montant(masse['total_cnss'])}")
        print(f"   • Total AMO: {formater_montant(masse['total_amo'])}")
        print(f"   • Total IR: {formater_montant(masse['total_ir'])}")
        print(f"   • Total charges: {formater_montant(masse['total_charges'])}")

    def menu_transactions(self):
        """Menu de gestion des transactions"""
        options = [
            "Enregistrer une vente",
            "Enregistrer un achat",
            "Lister les transactions",
            "Voir les ventes du mois",
            "Voir les achats du mois",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== GESTION DES TRANSACTIONS ===")

        if choix == 0:
            self.enregistrer_vente()
        elif choix == 1:
            self.enregistrer_achat()
        elif choix == 2:
            self.lister_transactions()
        elif choix == 3:
            self.voir_ventes_mois()
        elif choix == 4:
            self.voir_achats_mois()

    def enregistrer_vente(self):
        """Enregistre une vente"""
        print("\n=== ENREGISTRER UNE VENTE ===")

        # Sélectionner le client
        clients = Client.get_all()
        if not clients:
            print("❌ Aucun client enregistré. Ajoutez d'abord un client.")
            return

        print("\nClients disponibles:")
        for i, client in enumerate(clients):
            print(f"  {i+1}. {client.nom}")

        try:
            choix_client = int(input("Choisissez un client: ")) - 1
            if choix_client < 0 or choix_client >= len(clients):
                print("❌ Choix invalide")
                return
            client = clients[choix_client]
        except ValueError:
            print("❌ Choix invalide")
            return

        # Sélectionner le produit
        produits = Produit.get_all()
        if not produits:
            print("❌ Aucun produit enregistré. Ajoutez d'abord un produit.")
            return

        print("\nProduits disponibles:")
        for i, produit in enumerate(produits):
            print(f"  {i+1}. {produit.nom} - Stock: {produit.quantite} {produit.unite} - Prix: {formater_montant(produit.prix_vente)}")

        try:
            choix_produit = int(input("Choisissez un produit: ")) - 1
            if choix_produit < 0 or choix_produit >= len(produits):
                print("❌ Choix invalide")
                return
            produit = produits[choix_produit]
        except ValueError:
            print("❌ Choix invalide")
            return

        # Quantité
        quantite_str = input(f"Quantité à vendre (max: {produit.quantite}): ").strip()
        quantite = valider_quantite(quantite_str)
        if quantite is None:
            return

        if quantite > produit.quantite:
            print("❌ Stock insuffisant")
            return

        # Prix unitaire (optionnel)
        prix_str = input(f"Prix unitaire ({formater_montant(produit.prix_vente)} par défaut): ").strip()
        prix_unitaire = valider_montant(prix_str) if prix_str else produit.prix_vente

        try:
            transaction = Transaction.enregistrer_transaction(
                type="vente",
                type_entite="client",
                entite_id=client.id,
                produit_id=produit.id,
                quantite=quantite,
                prix_unitaire=prix_unitaire
            )
            print(f"✅ Vente enregistrée: {formater_montant(transaction.total)}")
        except Exception as e:
            print(f"❌ Erreur lors de l'enregistrement: {e}")

    def enregistrer_achat(self):
        """Enregistre un achat"""
        print("\n=== ENREGISTRER UN ACHAT ===")

        # Sélectionner le fournisseur
        fournisseurs = Fournisseur.get_all()
        if not fournisseurs:
            print("❌ Aucun fournisseur enregistré. Ajoutez d'abord un fournisseur.")
            return

        print("\nFournisseurs disponibles:")
        for i, fournisseur in enumerate(fournisseurs):
            print(f"  {i+1}. {fournisseur.nom}")

        try:
            choix_fournisseur = int(input("Choisissez un fournisseur: ")) - 1
            if choix_fournisseur < 0 or choix_fournisseur >= len(fournisseurs):
                print("❌ Choix invalide")
                return
            fournisseur = fournisseurs[choix_fournisseur]
        except ValueError:
            print("❌ Choix invalide")
            return

        # Sélectionner le produit
        produits = Produit.get_all()
        if not produits:
            print("❌ Aucun produit enregistré. Ajoutez d'abord un produit.")
            return

        print("\nProduits disponibles:")
        for i, produit in enumerate(produits):
            print(f"  {i+1}. {produit.nom} - Prix d'achat: {formater_montant(produit.prix_achat)}")

        try:
            choix_produit = int(input("Choisissez un produit: ")) - 1
            if choix_produit < 0 or choix_produit >= len(produits):
                print("❌ Choix invalide")
                return
            produit = produits[choix_produit]
        except ValueError:
            print("❌ Choix invalide")
            return

        # Quantité
        quantite_str = input("Quantité à acheter: ").strip()
        quantite = valider_quantite(quantite_str)
        if quantite is None:
            return

        # Prix unitaire (optionnel)
        prix_str = input(f"Prix unitaire ({formater_montant(produit.prix_achat)} par défaut): ").strip()
        prix_unitaire = valider_montant(prix_str) if prix_str else produit.prix_achat

        try:
            transaction = Transaction.enregistrer_transaction(
                type="achat",
                type_entite="fournisseur",
                entite_id=fournisseur.id,
                produit_id=produit.id,
                quantite=quantite,
                prix_unitaire=prix_unitaire
            )
            print(f"✅ Achat enregistré: {formater_montant(transaction.total)}")
        except Exception as e:
            print(f"❌ Erreur lors de l'enregistrement: {e}")

    def lister_transactions(self):
        """Liste toutes les transactions"""
        transactions = Transaction.get_all()

        if not transactions:
            print("\n📋 Aucune transaction enregistrée")
            return

        # Limiter à 20 dernières transactions
        transactions_recentes = transactions[:20]

        donnees = []
        for transaction in transactions_recentes:
            donnees.append({
                'ID': transaction.id,
                'Type': transaction.type.upper(),
                'Entité': transaction.get_entite_nom(),
                'Produit': transaction.get_produit_nom(),
                'Quantité': transaction.quantite,
                'Total': formater_montant(transaction.total),
                'Date': transaction.date_transaction.strftime('%d/%m/%Y')
            })

        afficher_tableau(donnees, ['ID', 'Type', 'Entité', 'Produit', 'Quantité', 'Total', 'Date'],
                        "DERNIÈRES TRANSACTIONS")

        if len(transactions) > 20:
            print(f"\n(Affichage des 20 dernières transactions sur {len(transactions)} total)")

    def voir_ventes_mois(self):
        """Affiche les ventes du mois"""
        mois = input("Mois (YYYY-MM, ex: 2024-01): ").strip()

        try:
            # Valider le format
            datetime.strptime(mois, "%Y-%m")

            ventes = Transaction.get_by_type("vente")
            ventes_mois = [v for v in ventes if v.date_transaction.strftime("%Y-%m") == mois]

            if not ventes_mois:
                print(f"\n📋 Aucune vente pour {mois}")
                return

            donnees = []
            total_ca = 0

            for vente in ventes_mois:
                donnees.append({
                    'Date': vente.date_transaction.strftime('%d/%m'),
                    'Client': vente.get_entite_nom(),
                    'Produit': vente.get_produit_nom(),
                    'Quantité': vente.quantite,
                    'Total': formater_montant(vente.total)
                })
                total_ca += vente.total

            afficher_tableau(donnees, ['Date', 'Client', 'Produit', 'Quantité', 'Total'],
                            f"VENTES DE {mois}")
            print(f"\n💰 Chiffre d'affaires total: {formater_montant(total_ca)}")

        except ValueError:
            print("❌ Format de mois invalide. Utilisez YYYY-MM")

    def voir_achats_mois(self):
        """Affiche les achats du mois"""
        mois = input("Mois (YYYY-MM, ex: 2024-01): ").strip()

        try:
            # Valider le format
            datetime.strptime(mois, "%Y-%m")

            achats = Transaction.get_by_type("achat")
            achats_mois = [a for a in achats if a.date_transaction.strftime("%Y-%m") == mois]

            if not achats_mois:
                print(f"\n📋 Aucun achat pour {mois}")
                return

            donnees = []
            total_achats = 0

            for achat in achats_mois:
                donnees.append({
                    'Date': achat.date_transaction.strftime('%d/%m'),
                    'Fournisseur': achat.get_entite_nom(),
                    'Produit': achat.get_produit_nom(),
                    'Quantité': achat.quantite,
                    'Total': formater_montant(achat.total)
                })
                total_achats += achat.total

            afficher_tableau(donnees, ['Date', 'Fournisseur', 'Produit', 'Quantité', 'Total'],
                            f"ACHATS DE {mois}")
            print(f"\n💸 Total des achats: {formater_montant(total_achats)}")

        except ValueError:
            print("❌ Format de mois invalide. Utilisez YYYY-MM")

    def menu_declarations(self):
        """Menu des déclarations fiscales"""
        options = [
            "Déclarer TVA",
            "Déclarer IS",
            "Déclarer IR",
            "Voir les déclarations",
            "Générer déclarations sociales",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== DÉCLARATIONS FISCALES ===")

        if choix == 0:
            self.declarer_tva()
        elif choix == 1:
            self.declarer_is()
        elif choix == 2:
            self.declarer_ir()
        elif choix == 3:
            self.voir_declarations_fiscales()
        elif choix == 4:
            self.generer_declarations_sociales()

    def declarer_tva(self):
        """Déclare la TVA"""
        periode = input("Période (YYYY-MM): ").strip()
        montant_str = input("Montant TVA à payer: ").strip()

        montant = valider_montant(montant_str)
        if montant is None:
            return

        declaration = declarer_impot("TVA", periode, montant)
        print(f"✅ Déclaration TVA enregistrée pour {periode}")

    def declarer_is(self):
        """Déclare l'IS"""
        annee = input("Année (YYYY): ").strip()
        montant_str = input("Montant IS à payer: ").strip()

        montant = valider_montant(montant_str)
        if montant is None:
            return

        declaration = declarer_impot("IS", annee, montant)
        print(f"✅ Déclaration IS enregistrée pour {annee}")

    def declarer_ir(self):
        """Déclare l'IR"""
        periode = input("Période (YYYY-MM): ").strip()
        montant_str = input("Montant IR à payer: ").strip()

        montant = valider_montant(montant_str)
        if montant is None:
            return

        declaration = declarer_impot("IR", periode, montant)
        print(f"✅ Déclaration IR enregistrée pour {periode}")

    def voir_declarations_fiscales(self):
        """Affiche les déclarations fiscales"""
        declarations = DeclarationFiscale.get_all()

        if not declarations:
            print("\n📋 Aucune déclaration fiscale enregistrée")
            return

        donnees = []
        for declaration in declarations:
            donnees.append({
                'Type': declaration.type_impot,
                'Période': declaration.periode,
                'Montant': formater_montant(declaration.montant),
                'Date': declaration.date_declaration.strftime('%d/%m/%Y')
            })

        afficher_tableau(donnees, ['Type', 'Période', 'Montant', 'Date'],
                        "DÉCLARATIONS FISCALES")

    def generer_declarations_sociales(self):
        """Génère les déclarations sociales"""
        mois = input("Mois (YYYY-MM): ").strip()

        try:
            datetime.strptime(mois, "%Y-%m")
            declarations = generer_declarations_sociales_mois(mois)
            print(f"✅ {len(declarations)} déclarations sociales générées pour {mois}")
        except ValueError:
            print("❌ Format de mois invalide. Utilisez YYYY-MM")

    def menu_rapports(self):
        """Menu des rapports et analyses"""
        options = [
            "Rapport mensuel",
            "Bilan annuel",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== RAPPORTS ET ANALYSES ===")

        if choix == 0:
            self.generer_rapport_mensuel()
        elif choix == 1:
            self.generer_bilan_annuel()

    def generer_rapport_mensuel(self):
        """Génère un rapport mensuel"""
        mois = input("Mois (YYYY-MM, ex: 2024-01): ").strip()

        try:
            # Valider le format
            datetime.strptime(mois, "%Y-%m")
            afficher_rapport_mensuel(mois)
        except ValueError:
            print("❌ Format de mois invalide. Utilisez YYYY-MM")

    def generer_bilan_annuel(self):
        """Génère un bilan annuel"""
        annee_str = input("Année (ex: 2024): ").strip()

        try:
            annee = int(annee_str)
            if annee < 2000 or annee > 2100:
                print("❌ Année invalide")
                return

            afficher_bilan_annuel(annee)
        except ValueError:
            print("❌ Format d'année invalide")

    def menu_utilitaires(self):
        """Menu des utilitaires"""
        options = [
            "Sauvegarder la base de données",
            "Initialiser des données de test",
            "Voir les statistiques",
            "Retour au menu principal"
        ]

        choix = saisir_choix(options, "=== UTILITAIRES ===")

        if choix == 0:
            sauvegarder_backup()
        elif choix == 1:
            self.initialiser_donnees_test()
        elif choix == 2:
            self.voir_statistiques()

    def initialiser_donnees_test(self):
        """Initialise des données de test"""
        if not confirmer_action("Créer des données de test ?"):
            return

        print("🔄 Création des données de test...")

        # Clients de test
        clients_test = [
            {"nom": "Station TOTAL Casablanca", "telephone": "0522123456", "adresse": "Bd Mohammed V, Casablanca"},
            {"nom": "Station AFRIQUIA Rabat", "telephone": "0537654321", "adresse": "Av Hassan II, Rabat"},
            {"nom": "Station SHELL Marrakech", "telephone": "0524987654", "adresse": "Route de Fès, Marrakech"}
        ]

        for client_data in clients_test:
            client = Client(**client_data)
            client.save()

        # Fournisseurs de test
        fournisseurs_test = [
            {"nom": "SAMIR Raffinerie", "telephone": "0523111222", "adresse": "Mohammedia"},
            {"nom": "Distributeur Carburants Maroc", "telephone": "0522333444", "adresse": "Casablanca"}
        ]

        for fournisseur_data in fournisseurs_test:
            fournisseur = Fournisseur(**fournisseur_data)
            fournisseur.save()

        # Produits de test
        produits_test = [
            {"nom": "Gasoil", "prix_achat": 12.50, "prix_vente": 14.20, "quantite": 5000},
            {"nom": "Essence Super", "prix_achat": 13.80, "prix_vente": 15.60, "quantite": 3000},
            {"nom": "Essence Sans Plomb", "prix_achat": 13.20, "prix_vente": 15.00, "quantite": 2500}
        ]

        for produit_data in produits_test:
            produit = Produit(**produit_data)
            produit.save()

        # Employés de test
        employes_test = [
            {"nom": "Ahmed BENALI", "cin": "AB123456", "numero_cnss": "1234567890", "salaire_brut": 8000},
            {"nom": "Fatima ALAOUI", "cin": "FA654321", "numero_cnss": "0987654321", "salaire_brut": 6500},
            {"nom": "Mohamed TAZI", "cin": "MT789012", "numero_cnss": "1122334455", "salaire_brut": 5500}
        ]

        for employe_data in employes_test:
            employe = Employe(**employe_data)
            employe.save()

        print("✅ Données de test créées avec succès!")

    def voir_statistiques(self):
        """Affiche les statistiques générales"""
        nb_clients = len(Client.get_all())
        nb_fournisseurs = len(Fournisseur.get_all())
        nb_produits = len(Produit.get_all())
        nb_employes = len(Employe.get_all())
        nb_transactions = len(Transaction.get_all())

        print(f"\n📊 STATISTIQUES GÉNÉRALES:")
        print(f"   • Clients: {nb_clients}")
        print(f"   • Fournisseurs: {nb_fournisseurs}")
        print(f"   • Produits: {nb_produits}")
        print(f"   • Employés: {nb_employes}")
        print(f"   • Transactions: {nb_transactions}")

        if nb_produits > 0:
            produits = Produit.get_all()
            valeur_stock = sum(p.calculer_valeur_stock() for p in produits)
            print(f"   • Valeur totale du stock: {formater_montant(valeur_stock)}")

        if nb_employes > 0:
            masse = Employe.calculer_masse_salariale()
            print(f"   • Masse salariale mensuelle: {formater_montant(masse['total_brut'])}")

    def quitter(self):
        """Quitte l'application"""
        if confirmer_action("Voulez-vous vraiment quitter ?"):
            print("👋 Au revoir!")
            self.running = False

def main():
    """Fonction principale"""
    app = ComptabiliteApp()
    app.demarrer()

if __name__ == "__main__":
    main()
